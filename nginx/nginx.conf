# 全局设置
user  nginx;  # 运行 Nginx 的用户
worker_processes  auto;  # 工作进程数量

# 事件设置
events {
    worker_connections  1024;  # 每个工作进程的最大连接数
}

# HTTP 设置
http {
    include       mime.types;  # 包含 MIME 类型
    default_type  application/octet-stream;  # 默认类型

    sendfile        on;  # 启用高效文件传输
    keepalive_timeout  65;  # 保持连接的超时时间

    # 服务器块
    server {
        listen 80;

        server_name developer.whgxwl.com;

        # Proxy requests to Next.js application
        location / {
            proxy_pass http://localhost:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
        }

        # Proxy API requests to Next.js API routes
        location /api {
            proxy_pass http://localhost:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
        }

        # swagger https://app.apifox.com/project/5741259
        location /apisix {
            proxy_pass https://open-dashboard.whgxwl.com;
        }

        # swagger https://apifox.com/apidoc/shared-bf7ccaa6-1dd8-4ceb-85bd-3b87848d5784/270138610e0
        location /auth {
            proxy_pass https://pre-platapi-auth-server.whgxwl.com;
        }

        # Serve static files from .next/static directory
        location /_next/static/ {
            alias /app/.next/static/;
        }

        location /public/ {
            alias /app/public/;
        }
    }
}
