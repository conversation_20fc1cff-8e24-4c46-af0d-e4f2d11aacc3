{"name": "developer-front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:test": "NODE_ENV=test next dev", "build": "next build", "build:test": "NODE_ENV=test next build", "build:prod": "NODE_ENV=production next build", "start": "next start", "start:test": "NODE_ENV=test next start", "start:prod": "NODE_ENV=production next start", "lint": "next lint", "lint-staged": "lint-staged", "prepare": "husky", "preinstall": "npx only-allow pnpm", "lint:eslint": "eslint src/**/*.{ts,tsx} --max-warnings=0", "lint:eslint:fix": "eslint src/**/*.{ts,tsx} --fix", "prettier": "prettier --write --ignore-unknown .", "prettier:check": "prettier --check --ignore-unknown .", "clean": "rm -rf node_modules && rm -rf dist", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "commit": "cz"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.1.7", "@sentry/nextjs": "^9.16.1", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "axios": "^1.8.3", "classnames": "^2.5.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dedent": "^1.5.3", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "next": "15.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-syntax-highlighter": "^15.6.1", "shiki": "^3.0.0"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@eslint/eslintrc": "^3", "@tailwindcss/typography": "^0.5.16", "@types/mdx": "^2.0.13", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "commitizen": "^4.3.1", "conventional-changelog-cli": "^5.0.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^9", "eslint-config-next": "15.1.7", "husky": "^9.1.7", "lint-staged": "^15.4.3", "postcss": "^8", "prettier": "^3.5.0", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.1", "typescript": "^5"}, "lint-staged": {"*": ["prettier --write --ignore-unknown"], "*.{js,jsx,ts,tsx}": ["eslint --fix"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}}