import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

  // 设置采样率，范围从0到1，1表示100%
  // 在生产环境中建议使用较低的值
  tracesSampleRate: 1.0,

  // 或者使用tracesSampler进行条件采样
  // tracesSampler: context => {
  //   // 根据环境或其他上下文信息调整采样率
  //   if (process.env.NODE_ENV === 'production') {
  //     return 0.1;
  //   }
  //   return 1.0;
  // },

  // 启用性能监控
  // enableTracing: true,

  // 设置环境
  environment: process.env.NODE_ENV,

  // 调试模式（在开发环境下可以开启）
  debug: process.env.NODE_ENV !== 'production',

  // 集成列表
  integrations: [Sentry.replayIntegration()],

  // 浏览器专用配置
  replaysSessionSampleRate: 0.1, // 会话重放采样率
  replaysOnErrorSampleRate: 1.0, // 错误重放采样率
})

export const onRouterTransitionStart = Sentry.captureRouterTransitionStart
