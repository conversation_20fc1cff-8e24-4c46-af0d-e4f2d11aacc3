# OpenApi

## [Next](https://nextjs.org/)

初始化项目

```bash
# init
npx create-next-app@latest --skip-install

# dev
pnpm run dev
```

git hooks

```bash
# git-hooks
pnpm i @commitlint/cli @commitlint/config-conventional commitizen conventional-changelog-cli cz-conventional-changelog lint-staged prettier husky -D

# husky init
pnpm exec husky init
```

## 备忘

[PRD](https://mah2eds8ab.feishu.cn/docx/CcSMdNVtDooac8xB8pncBEHln5f)
[设计稿](https://mastergo.com/file/143633216128733)
[swagger](https://app.apifox.com/project/5741259)
[swagger](https://apifox.com/apidoc/shared-bf7ccaa6-1dd8-4ceb-85bd-3b87848d5784/270138610e0)

## 部署

[with-docker](https://github.com/vercel/next.js/blob/canary/examples/with-docker/Dockerfile)
[with-docker-multi-env](https://github.com/vercel/next.js/blob/canary/examples/with-docker-multi-env/docker/staging/Dockerfile)

```bash
docker build -t nextjs-nginx:latest -f Dockerfile .
docker run --name next-web-app -p 80:80 -p 3000:3000 nextjs-nginx
```
