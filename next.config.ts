import type { NextConfig } from 'next'
import createMDX from '@next/mdx'
import { withSentryConfig } from '@sentry/nextjs'

const nextConfig: NextConfig = {
  // output: 'standalone',
  assetPrefix: process.env.NEXT_PUBLIC_CDN_URL || '',
  // Configure `pageExtensions` to include markdown and MDX files
  pageExtensions: ['js', 'jsx', 'md', 'mdx', 'ts', 'tsx'],
  // Optionally, add any other Next.js config below
  reactStrictMode: true,
  cacheMaxMemorySize: 0,
  // 启用 sourceMap 配置
  // productionBrowserSourceMaps: true,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
      },
    ],
  },
  async redirects() {
    return [
      {
        source: '/guides',
        destination: '/guides/startedGuide',
        permanent: false,
      },
      {
        source: '/workbench',
        destination: '/workbench/account',
        permanent: false,
      },
    ]
  },
  async rewrites() {
    return [
      {
        source: '/apisix/:path*',
        destination: `${process.env.NEXT_DOCS_API_BASE_URL}/apisix/:path*`,
      },
      {
        source: '/auth/:path*',
        destination: `${process.env.NEXT_SEARCH_API_BASE_URL}/auth/:path*`,
      },
    ]
  },
}

const withMDX = createMDX({
  // Add markdown plugins here, as desired
  // By default only the `.mdx` extension is supported.
  extension: /\.mdx?$/,
  options: {
    /* otherOptions… */
  },
})

// Sentry配置选项
const sentryWebpackPluginOptions = {
  // 附加选项
  silent: true, // 禁止控制台输出
  org: process.env.SENTRY_ORG || '',
  project: process.env.SENTRY_PROJECT || '',
  authToken: process.env.SENTRY_AUTH_TOKEN || '',
  widenClientFileUpload: true,

  // 在开发过程中禁用上传sourcemaps
  dryRun: process.env.NODE_ENV !== 'production',

  // 调试构建
  debug: false,
}

// 首先应用MDX配置，然后应用Sentry配置
export default withSentryConfig(withMDX(nextConfig), sentryWebpackPluginOptions)
