{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "paths": {"@/*": ["./src/*"]}, "plugins": [{"name": "next"}]}, "include": ["**/*.mdx", "**/*.ts", "**/*.tsx", "declarations.d.ts", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules"]}