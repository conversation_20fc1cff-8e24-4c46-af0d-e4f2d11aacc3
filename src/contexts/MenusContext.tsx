import React, { createContext, useContext } from 'react'

export interface IMenusItem {
  title: string
  url: string
  icon?: string
  brief?: string
  children?: IMenusItem[]
}

const MenusContext = createContext<{
  menus: IMenusItem[]
}>({
  menus: [],
})

export const MenuProvider = ({
  menus,
  children,
}: {
  menus: IMenusItem[]
  children: React.ReactNode
}) => {
  return (
    <MenusContext.Provider value={{ menus }}>{children}</MenusContext.Provider>
  )
}

export const useMenu = () => useContext(MenusContext)
