@import './typography.css';
@tailwind base;
@tailwind components;

@tailwind utilities;

@layer components {
  .line-code {
    @apply whitespace-pre-wrap break-all;
    span.linenumber {
      + span + span {
        white-space: nowrap;
      }
    }
  }
}

@layer components {
  .custom-checkbox {
    @apply relative inline-block h-[18px] w-[18px] appearance-none rounded-none border-none bg-transparent p-0 align-middle text-[18px] leading-none outline-none;
  }

  .custom-checkbox::before {
    font-family: 'iconfont' !important;
    content: '\f043';
    @apply cursor-pointer text-[#cccccc] transition-all duration-300;
  }

  .custom-checkbox:hover::before {
    content: '\f043';
    @apply text-[#707070];
  }

  .custom-checkbox[halfchecked='true']::before {
    content: '\e287';
    @apply text-[#707070];
  }

  .custom-checkbox[halfchecked='true']:hover::before {
    @apply text-[#707070];
  }

  .custom-checkbox:checked::before {
    content: '\f186';
    font-family: 'iconfont' !important;
    @apply text-[#707070];
  }

  .custom-checkbox:checked:hover::before {
    @apply text-[#707070];
  }

  .custom-checkbox:disabled::before {
    @apply text-[#cccccc];
  }
}

:root {
  --background: #ffffff;
  --foreground: #19191a;
  --textColorBlack: #19191a;
  --textColorGray: #707070;
  --textColorLink: #0060bf;
  --tableHeaderPrimary: rgba(64, 128, 255, 0.18);
  --tableHeaderBorderPrimary: rgb(193, 213, 255);
}

html {
  scroll-behavior: smooth;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}
.transform_rotateX_180 {
  transform: rotateX(180deg);
}
