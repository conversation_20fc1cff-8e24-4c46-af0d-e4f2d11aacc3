.prose {
  --tw-prose-links: #0060bf;
}

.prose :where(table):not(:where([class~='not-prose'], [class~='not-prose'] *)) {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}
.prose
  :where(.prose > :first-child):not(
    :where([class~='not-prose'], [class~='not-prose'] *)
  ) {
  display: none !important;
}

.prose .docs-info > p {
  margin: 0 !important;
}
.prose
  :where(ul > li):not(:where([class~='not-prose'], [class~='not-prose'] *)) {
  padding-inline-start: 0 !important;
}

.prose :where(p):not(:where([class~='not-prose'], [class~='not-prose'] *)) {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}

.prose :where(li):not(:where([class~='not-prose'], [class~='not-prose'] *)),
.prose
  :where(.prose > ol > li > p:last-child):not(
    :where([class~='not-prose'], [class~='not-prose'] *)
  ),
.prose
  :where(.prose > ol > li > p:first-child):not(
    :where([class~='not-prose'], [class~='not-prose'] *)
  ) {
  margin-top: 8px !important;
  margin-bottom: 8px !important;
}
