import fs from 'node:fs/promises'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

export async function getDocPageBySlug(slug: string): Promise<null | {
  Component: React.FC
  title: string
  updatedAt: string
}> {
  try {
    // Check if the file exists
    if (
      !(await fs
        .stat(path.join(process.cwd(), './src/docs', `${slug}.mdx`))
        .catch(() => false))
    ) {
      return null
    }

    // eslint-disable-next-line @next/next/no-assign-module-variable
    const module = await import(`../docs/${slug}.mdx`)
    if (!module.default) {
      return null
    }

    return {
      Component: module.default,
      title: module.meta.title,
      updatedAt: module.meta.updatedAt,
    }
  } catch (e) {
    console.error(e)
    return null
  }
}

export async function getDocPageSlugs() {
  const slugs = []
  for (const file of await fs.readdir(path.join(__dirname, '../docs'))) {
    if (!file.endsWith('.mdx')) continue
    slugs.push(path.parse(file).name)
  }
  return slugs
}
