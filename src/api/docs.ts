import { get } from '@/utils'

const API_BASE_URL = process.env.NEXT_DOCS_API_BASE_URL || ''
const API_SEARCH_BASE_URL = process.env.NEXT_SEARCH_API_BASE_URL || ''

export enum ServiceType {
  Order = 1,
  Item = 2,
}

export interface IParameter {
  name: string
  in: string
  type: string
  desc: string
  required: boolean
  children: IParameter[]
}

export interface IResponse {
  name: string
  type: string
  desc: string
  children: IResponse[]
}

export interface IDocs {
  id: number
  createdAt: string
  updatedAt: string
  description: string
  document: {
    summary: string
    desc: string
    httpMethod: string
    path: string
    contentType: string
    parameters: IParameter[]
    responseStatus: { code: string; desc: string }[]
    responses: IResponse[]
    mockResponse: any
  }
}

export interface IDocsResponse {
  serviceType: ServiceType
  docDetailList: Array<IDocs>
}

export interface IDocsResData extends IDocs {
  serviceType: ServiceType
}

interface ResDoc<T> {
  code: number
  message?: string
  data: T
}

export interface IDocsGroup {
  [ServiceType.Order]: IDocsResData[]
  [ServiceType.Item]: IDocsResData[]
}

/**
 * 查询所有接口文档
 * @returns
 */
export async function getDocs(): Promise<IDocsGroup> {
  const base: IDocsGroup = {
    [ServiceType.Order]: [],
    [ServiceType.Item]: [],
  }
  try {
    const res = await get<ResDoc<IDocsResponse[] | null>>(
      `${API_BASE_URL}/apisix/doc/list`,
    )
      .then((res) => {
        if (res.code !== 0) {
          throw new Error(res.message)
        }
        return res?.data
      })
      .catch(() => null)
    const groups = (res || []).reduce((t, c: IDocsResponse) => {
      const { serviceType, docDetailList = [] } = c
      const list = docDetailList.map((v) => ({ ...v, serviceType }))
      t[c.serviceType] = list
      return t
    }, base)
    return groups
  } catch (e) {
    console.error(e)
    return base
  }
}

/**
 * 获取slugs
 * @returns
 */
export async function getDocSlugs() {
  const groups = await getDocs()
  const list = [...groups[ServiceType.Order], ...groups[ServiceType.Item]]
  return list.map((v) => v.id.toString())
}

/**
 * 文档详见
 * - https://mah2eds8ab.feishu.cn/docx/Zp48dCurLopjInxXjYgcfa7vnv8
 * - https://mah2eds8ab.feishu.cn/docx/GR1Sdac8KoxxNGxWxmWcXvvtnSf
 */
export const MapMockResponse = {
  /**
   * 获取商品库存信息API  @吴奇
   */
  '/item/inventory': {
    code: 200,
    message: 'Success',
    traceId: 'POSC653848188854341',
    extend: null,
    data: [
      {
        productId: 11552,
        productName:
          'Cisco SFP-10G-SR Compatible 10GBASE-SR SFP+ 850nm 300m DOM Duplex LC/UPC MMF Optical Transceiver Module',
        totalStock: 616549,
        warehouseDetail: [
          {
            stockQuantity: 23395,
            warehouseName: 'EU Warehouse',
          },
          {
            stockQuantity: 51288,
            warehouseName: 'US Warehouse',
          },
          {
            stockQuantity: 383936,
            warehouseName: 'Global Warehouse',
          },
          {
            stockQuantity: 140655,
            warehouseName: 'SG Warehouse',
          },
          {
            stockQuantity: 8522,
            warehouseName: 'AU Warehouse',
          },
          {
            stockQuantity: 1397,
            warehouseName: 'JP Warehouse',
          },
          {
            stockQuantity: 7356,
            warehouseName: 'UK Warehouse',
          },
        ],
      },
    ],
  },
  /**
   * 获取商品参数API @易文杰
   */
  '/item/detail': {
    code: '200',
    msg: 'SUCCESS',
    data: {
      itemDetails: [
        {
          productCategories: 'Testers & Tools',
          productModel: 'FOCC-702',
          productName:
            'OAM Fiber Optic Cassette Cleaner for LC/SC/FC/ST/D4/DIN Connector (500 cleans)',
          productPrice: 'US$43.00',
          productWeight: '0.00',
          productsId: '11160',
          warranty: '0',
        },
      ],
    },
  },
  /**
   * 获取订单状态API @傅星辰
   */
  '/order/status': {
    code: 200,
    message: 'Success',
    traceId: 'gmkU655996444807237',
    extend: null,
    data: [
      {
        orderId: 'FS1603****3320',
        purchaseOrderNumber: ' PO **** ',
        orderStatus: 'delivery',
        customerInfo: {
          customerEmail: '<EMAIL>',
          customerName: 'j***n.c**n',
          customerNo: '580****012',
        },
        creationTime: '06/30/2016 21:40:46 EDT',
        lastUpdatedTime: '07/01/2016 17:58:19 EDT',
      },
    ],
  },
  /**
   * 3.2.2 ✅获取订单信息API @傅星辰
   */
  '/order/detail': {
    code: 200,
    message: 'Success',
    traceId: 'YLYd656241749090373',
    extend: null,
    data: [
      {
        orderId: 'FS1607****1320',
        purchaseOrderNumber: '',
        orderStatue: 'delivery',
        customerInfo: {
          customerEmail: '<EMAIL>',
          customerName: 'j***n.c**n',
          customerNo: '580****201',
        },
        creationTime: '06/30/2016 21:40:46 EDT',
        lastUpdatedTime: '07/01/2016 17:58:19 EDT',
        amount: {
          totalAmount: 400.05,
          excludingTaxes: 400.05,
          freight: 0.0,
          taxes: 0.0,
          currencyType: 'USD',
          insPrice: 0.0,
          ddpPrice: 0.0,
        },
        productList: [
          {
            productId: '41042',
            productName:
              'OAM Fiber Optic Cassette Cleaner for LC/SC/FC/ST/D4/DIN Connector (500 cleans)',
            quantity: '10',
            unitPrice: 'US$9.56',
          },
        ],
        shippingAddress: {
          firstName: 'FS',
          lastName: 'developer',
          postCode: '9***1',
          telephone: '85****04',
          countryRegion: 'United States',
          stateProvince: 'CA',
          city: 'San Diego',
          address1: '******',
          address2: '',
        },
        billingAddress: {
          firstName: 'FS',
          lastName: 'developer',
          postCode: '9***1',
          telephone: '85****04',
          countryRegion: 'United States',
          stateProvince: 'CA',
          city: 'San Diego',
          address1: '******',
          address2: '',
        },
        pickupDetail: {
          customerName: '',
          email: '',
          contactPhoneNumber: '',
          pickupDate: '',
          startingTimeForPickup: '',
          endTimeOfPickup: '',
          pickupAddress: '',
        },
      },
    ],
  },
  /**
   * 3.2.3 ✅获取订单物流信息 API @傅星辰
   */
  '/order/shipping': {
    code: 200,
    message: 'Success',
    traceId: 'YLYd656241858449477',
    extend: null,
    data: [
      {
        orderId: 'FS1608****3646',
        purchaseOrderNumber: '',
        subOrderCountInMaster: '1',
        carrier: 'FedEx',
        trackingNumber: '6730****1291',
        trackingDetails: [
          {
            description: 'Shipment information sent to FedEx',
            time: '2019-06-20 04:40:51',
          },
        ],
      },
    ],
  },
  /**
   * 3.2.4 ✅获取订单发票信息 API @刘峻伯
   */
  '/order/invoice': {
    code: 200,
    message: 'Success',
    traceId: 'Rcnc655635508027461',
    extend: null,
    data: [
      {
        orderId: 'FS2408****6381',
        purchaseOrderNumber: null,
        orderStatus: 'delivered',
        message: "Only shipped items' invoice info.",
        ordersInvoiceList: [
          {
            invoiceId: 'IN0424****0059',
            amount: {
              totalAmount: 400.05,
              excludingTaxes: 315.5,
              freight: 0.0,
              taxes: 84.55,
              insPrice: 0.0,
              ddpPrice: 0.0,
              customsDuty: 0.0,
              certificationFee: 0.0,
              serviceCharge: 0.0,
            },
            currency: 'EUR',
            invoiceDate: '08/01/2024 23:58:06 EDT',
            items: [
              {
                productId: 89572,
                productName:
                  'Ubiquiti UF-RJ45-10G Compatible SFP+ 10GBASE-T Copper 30m RJ-45 Transceiver Module (LOS)',
                quantity: 5,
                unitPrice: 17.53,
                subtotal: 87.65,
              },
            ],
            issuerInfo: [
              'FS.COM GmbH',
              'Röntgenstrasse **',
              '8***7 Karlsfeld',
              'Germany',
              'Tel.: +49 (0) 81****08',
              'VAT No.DE3*******1',
              'WEEE-Reg.-No.: DE5******1',
            ],
            buyerInfo: {
              buyerName: 'FS Developer',
              buyerCompany: 'FS',
              buyerTaxId: 'DE1*******5',
            },
            downloadUrl:
              'https://pvt-doc.fs.com/f83835fe8******ca9be81c******d5f7?AWSAccessKeyId=AKIAZ36***VAY***UW&Expires=1828603666&Signature=YvXtGuJ***SrH5do%3D',
          },
        ],
      },
    ],
  },
  /**
   * 3.2.5 ✅获取订单支付信息API @吴聪
   */
  '/order/payInfo': {
    code: 200,
    message: 'Success',
    traceId: 'mUZE656239857905733',
    extend: null,
    data: [
      {
        ordersNumber: 'FS2405****9386',
        purchaseOrderNumber: 'PO ******',
        paymentStatus: 'paid',
        paymentMethod: 'Purchase Order',
        paymentAmount: '91.89',
        currencyType: 'USD',
        paymentTime: '06/21/2024 08:00:00 EDT',
      },
    ],
  },
  /**
   * 3.2.6 ✅获取订单商品质保信息API @傅星辰
   */
  '/order/warranty': {
    code: 200,
    message: 'Success',
    traceId: 'YLYd656241895878725',
    extend: null,
    data: [
      {
        orderId: 'FS16070****9310',
        purchaseOrderNumber: '',
        productList: [
          {
            productId: '41042',
            productName:
              'Ubiquiti UF-MM-10G Compatible SFP+ 10GBASE-SR 850nm 300m DOM Duplex LC/UPC MMF Optical Transceiver Module',
            quantity: '10',
            switchSeriesName: '',
            switchModelName: '',
            warrantyDescription: '',
            warrantyStartDate: '00000000',
            warrantyEndDate: '00000000',
            serialNumber: 'C241****384',
            softwareServiceDeliveryDate: '00000000',
            softwareServiceStartDate: '00000000',
            softwareServiceEndDate: '00000000',
          },
        ],
      },
    ],
  },
  /**
   * 3.2.7  ✅获取年度产品报告API @刘峻伯
   */
  '/item/report': {
    code: 200,
    message: 'Success',
    data: {
      downloadUrl: 'https://fs.com/report/product/download******.xlsx',
      generatedAt: '2022-12-01T09:15:00Z',
    },
  },
  /**
   * 3.2.8  ✅获取Quote详情API @刘峻伯
   */
  '/quote/detail': {
    code: 200,
    message: 'Success',
    data: {
      quoteNumber: 'RQN202****00011',
      createdBy: 'fs developer',
      email: '<EMAIL>',
      createdAt: '2023-11-25T09:00:00Z',
      status: 'Active',
      expiration: '2023-12-31T23:59:59Z',
      lastUpdated: '2023-11-20T14:30:00Z',
      team: 'developer',
      items: [
        {
          sku: '11552',
          productName: 'Wireless Router X9',
          unitPrice: 'US$205.82',
          quantity: 5,
          totalPrice: 'US$205.82',
        },
        {
          sku: '11553',
          productName: 'Wireless X112',
          unitPrice: 'US$205.82',
          quantity: 5,
          totalPrice: 'US$205.82',
        },
      ],
      shippingAddress: {
        firstName: 'FS',
        lastName: 'developer',
        postCode: '9***1',
        telephone: '85****04',
        countryRegion: 'United States',
        stateProvince: 'CA',
        city: 'San Diego',
        address1: '******',
        address2: '',
      },
      billingAddress: {
        firstName: 'FS',
        lastName: 'developer',
        postCode: '9***1',
        telephone: '85****04',
        countryRegion: 'United States',
        stateProvince: 'CA',
        city: 'San Diego',
        address1: '******',
        address2: '',
      },
      shippingMethod: {
        freight: 205.82,
        shippingMethod: ['DHL'],
      },
      totalAmount: {
        total: 'US$205.82',
        excludingTaxes: 'US$205.82',
        freight: 'US$205.82',
        taxes: 'US$205.82',
        insPrice: 'US$205.82',
        ddpPrice: 'US$205.82',
        currencyType: 'USD',
      },
    },
  },
}

function normalizePath(path: string) {
  return (
    '/' +
    path
      .replace(/^\/|\/$/g, '')
      .split('/')
      .filter(Boolean)
      .join('/')
  )
}

/**
 * 根据文档ID查询文档详情
 * @returns
 */
export async function getDocsById(id: string) {
  try {
    const res = await get<ResDoc<IDocsResData>>(
      `${API_BASE_URL}/apisix/doc/getDocument/${id}`,
    )
    if (res.code !== 0) {
      throw new Error(res.message)
    }
    const { document, ...rest } = res?.data || {}
    const key =
      `${normalizePath(document.path.trim())}` as keyof typeof MapMockResponse

    console.log(key, 'key11111111')

    Object.assign(document, { mockResponse: MapMockResponse?.[key] || null })
    return {
      ...rest,
      document,
    }
  } catch (e) {
    throw e
  }
}

/**
 * 重试机制
 * @param fetch
 * @param times
 * @returns
 */
const withRetry = (fetch: (...args: any[]) => Promise<any>, times = 3) => {
  return async (...args: any) => {
    for (let i = 0; i < times; i++) {
      console.log('retry->', i, args)
      try {
        return await fetch(...args)
      } catch (e) {
        await new Promise((resolve) =>
          setTimeout(resolve, 1000 * Math.pow(2, i)),
        )
        if (i === times - 1) {
          throw e
        }
      }
    }
  }
}

export const getDocsByIdWithRetry = withRetry(getDocsById, 5)

export interface ISearchResData {
  id: number
  summary: string
  desc: string
}
/**
 * 搜索
 * @param v
 * @returns
 */
export async function doSearch(v?: string): Promise<ISearchResData[]> {
  if (!v) {
    return Promise.resolve([])
  }
  try {
    const res = await get<ResDoc<ISearchResData[]>>(
      `${API_SEARCH_BASE_URL}/auth/document/search?search=${v}`,
    )
    if (res.code !== 200) {
      throw new Error(res.message)
    }
    const list = res?.data || []
    return list
  } catch {
    return []
  }
}

const genMenus = (datas: IDocsResData[]) => {
  return datas.map((v) => ({
    title: v?.document?.summary,
    url: `/docs/${v.id}`,
  }))
}

/**
 * 获取菜单
 */
export async function getMenus() {
  const groups = await getDocs()

  const itemMenu = {
    title: 'Item Management',
    url: `/docs?serviceType=${ServiceType.Item}`,
    icon: '/items.svg',
    brief:
      'The Item Management API is designed to fetch product details and check inventory levels for seamless integration.',
    children: genMenus(groups[ServiceType.Item]),
  }

  const orderMenu = {
    title: 'Order Management',
    url: `/docs?serviceType=${ServiceType.Order}`,
    icon: '/order.svg',
    brief:
      'The Order Management service group contains functions that will allow you to perform order related operations such as get order info, status, warranty, etc.',
    children: genMenus(groups[ServiceType.Order]),
  }

  const menus = [
    {
      title: 'Guides',
      url: '/guides',
      icon: '/guides.svg',
      brief:
        'FS Developer Portal is an open platform designed for developers and partners to provide efficient and reliable technical support, enabling seamless integration of our services and features.',
      children: [
        {
          title: 'Getting Started Guide',
          url: '/guides/startedGuide',
        },
        {
          title: 'FS API Authentication and Authorization',
          url: '/guides/fsAuthentication',
        },
      ],
    },
    itemMenu,
    orderMenu,
    {
      title: 'Contact Us',
      url: '/guides/contactUs',
    },
  ]
  return menus
}
