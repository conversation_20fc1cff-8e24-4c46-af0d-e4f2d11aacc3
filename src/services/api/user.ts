import http from '../http'

export interface User {
  id: number
  name: string
  email: string
}

export interface LoginParams {
  email: string
  password: string
}

export const userApi = {
  // 企业注册
  register: (params: any) => {
    return http.post('/api/user/register', params)
  },
  // 验证邮箱
  verifyEmail: (params: any) => {
    return http.post('/api/user/checkEmail', params)
  },
  // 重新发送验证邮件
  resendVerifyEmail: (params: any) => {
    return http.post('/api/user/reSend', params)
  },
  // 激活邮箱
  activeEmail: (params: any) => {
    return http.post('/api/user/active', params)
  },
  // 加入公司
  joinCompany: (url: string, params: any) => {
    return http.post(url, params)
  },
  // 保持个人账户
  keepPersonalAccount: (url: string, params: any) => {
    return http.post(url, params)
  },
  // 获取加入页面信息
  getJoinCompanyInfo: () => {
    return http.get('/api/companyAccount/getJoinCompanyPageForCurrent')
  },
  // 通过cid获取加入页面信息
  getJoinCompanyInfoByCid: (params: { cid: string }) => {
    return http.get('/api/companyAccount/getJoinCompanyPage', { params })
  },
  // 获取用户信息
  getUserInfo: (token: string) => {
    return http.get('/api/developer/userInfo', {
      headers: {
        Authorization: `${token}`,
      },
    })
  },
  // 登出
  logout: (token: string) => {
    return http.post('/api/user/logout', {
      headers: {
        Authorization: `${token}`,
      },
    })
  },
}

export default userApi
