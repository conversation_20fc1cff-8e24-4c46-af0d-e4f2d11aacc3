import http from '../http'

export const accountApi = {
  //获取工作台首页数据
  homePage() {
    return http.get('/api/developer/homePage')
  },
  // 企业账户申请
  accountApply<T>(params: T) {
    return http.post('/api/companyAccount/accountApply', params)
  },
  //加入企业
  getJoinCompanyPageForCurrent() {
    return http.get('/api/companyAccount/getJoinCompanyPageForCurrent')
  },
  //同意成为企业用户
  agreeJoin(bizAccountId: number) {
    return http.post('/api/companyAccount/applyJoinCompanyForCurrent', {
      bizAccountId,
    })
  },
  //保持个人用户
  keepPersonal(bizAccountId: number) {
    return http.post('/api/companyAccount/applyNotJoinCompanyForCurrent', {
      bizAccountId,
    })
  },
  //申请凭证
  applySecret<T>(params: T) {
    return http.post('/api/developer/apiSecretApply', params)
  },
}

export default accountApi
