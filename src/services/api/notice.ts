import http from '../http'
const baseURl =
  process.env.NEXT_PUBLIC_NOETIC_BASE_URL || 'http://localhost:3000/api'

const noticeApi = {
  //获取api列表数据
  getApiList(ids: string = '') {
    return http.get('/auth/document/listSimpleByIds', {
      params: { ids: ids },
      baseURL: baseURl,
    })
  },

  //获取消息列表
  getNoticeList<T>(params: T) {
    return http.get('/auth/notice/listByCustomer', {
      params,
      baseURL: baseURl,
    })
  },
  //更新消息状态
  updateNoticeStatus(id: number) {
    return http.post('/auth/notice/changeRead', { id }, { baseURL: baseURl })
  },

  //确认密钥已读
  setSecretRead(id: string) {
    return http.post('/auth/client/look', { id }, { baseURL: baseURl })
  },

  //重新生成密钥
  getSecretKeyNew(id: string) {
    return http.post('/auth/client/refresh', { id }, { baseURL: baseURl })
  },
  //获取密钥id和密钥token
  getSecretDetail(id: string) {
    return http.get('/auth/user/getClientById', {
      params: { id },
      baseURL: baseURl,
    })
  },
}

export default noticeApi
