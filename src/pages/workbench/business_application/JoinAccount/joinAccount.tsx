import FsButton from '@/components/ui/FsButton'
import { FC, useEffect, useState } from 'react'
import accountApi from '@/services/api/account'
import message from '@/components/ui/Message'
import { useRouter } from 'next/router'
import { CheckOutlined } from '@ant-design/icons'

interface UserInfoType {
  companyName: string
  bizAccountId: number
  customerData: {
    customers_number_new: string
    customers_email_address: string
    customers_firstname: string
    customers_lastname: string
    email_is_active: string
  }
}

const WorkBenchPage: FC = () => {
  //用户信息
  const [userInfo, setUserInfo] = useState<UserInfoType>({
    companyName: '',
    bizAccountId: 0,
    customerData: {
      customers_number_new: '',
      customers_email_address: '',
      customers_firstname: '',
      customers_lastname: '',
      email_is_active: '',
    },
  })

  //loading
  const [loading, setLoading] = useState(false)

  const [isSuccess, setIsSuccess] = useState(false)

  const [isPersonal, setPersonal] = useState(false)

  //bizAccountId

  const router = useRouter()

  // 保持个人账户
  const keepPersonal = async () => {
    setLoading(true)
    try {
      const {
        code,
        date,
        message: msg,
      } = await accountApi
        .keepPersonal(userInfo.bizAccountId)
        .finally(() => setLoading(false))
      if (code === 200) {
        setIsSuccess(true)
        setPersonal(true)
      } else {
        message.error(msg)
      }
    } catch (error: any) {
      console.log(error, 'error')
      message.error(error.message as string)
    }
  }

  // 获取用户信息
  const getJoinCompany = async () => {
    try {
      const {
        code,
        data,
        message: msg,
      } = await accountApi.getJoinCompanyPageForCurrent()
      if (code === 200 && data) {
        setUserInfo((pre) => {
          return { ...pre, ...data }
        })
      } else {
        message.error(msg)
      }
    } catch (error: any) {
      message.error(error.message)
    }
  }

  //加入企业账户
  const joinBusinessAccount = async () => {
    setLoading(true)
    try {
      const {
        code,
        data,
        message: msg,
      } = await accountApi
        .agreeJoin(userInfo.bizAccountId)
        .finally(() => setLoading(false))
      if (code === 200 && data) {
        setIsSuccess(true)
        setPersonal(false)
      } else {
        message.error(msg)
      }
    } catch (error: any) {
      message.error(error.message)
    }
  }

  useEffect(() => {
    getJoinCompany()
  }, [])

  const handleBackHome = () => {
    router.replace('/')
  }

  if (isSuccess) {
    return (
      <div className="flex min-h-[calc(100vh-128px)] flex-col items-center justify-center bg-[#FAFAFB]">
        <div className="m-y-auto w-[680px] rounded-lg bg-white p-8">
          <div className="mb-2 flex items-center justify-center gap-x-2">
            <CheckOutlined className="text-2xl font-semibold text-[#10A300]" />{' '}
            <b className="text-xl font-semibold text-[#19191a]">
              Hi, August ({userInfo.customerData.customers_email_address})
            </b>
          </div>
          <div className="text-center text-sm text-[#707070]">
            {isPersonal ? (
              <p>
                {
                  'You have keep the Personal Account now. If you would like to join the business account, you can always contact your business account administrator or your account manager.'
                }
              </p>
            ) : (
              <p>{`The application has been submitted. Please wait for the Business Account administrator's confirmation. During this time, you can still access the website normally. Contact the account manager if you need help.`}</p>
            )}
          </div>
          <div className="pt-5 text-center">
            <FsButton onClick={handleBackHome}>Back to Homepage</FsButton>
          </div>
        </div>
      </div>
    )
  } else {
    return (
      <div className="flex min-h-[calc(100vh-128px)] flex-col items-center justify-center bg-[#FAFAFB]">
        <div className="m-y-auto w-[680px] rounded-lg bg-white p-8">
          <h2 className="mb-4 text-xl font-semibold text-[#19191A]">
            Join FS Business Account
          </h2>
          <p className="mb-2 text-sm text-[#707070]">
            We have found a business account matches your email address:{' '}
            {userInfo.companyName || ''} Innovation. And FS Open API is
            currently available exclusively to FS Business Account users.
          </p>
          <p className="mb-2 text-sm text-[#707070]">
            Before we continue, please review what this means and confirm that
            you are authorized to do so:
          </p>
          <ul className="flex flex-col gap-y-2 text-xs">
            <li className="relative pl-3">
              <b className="absolute left-0 top-[6px] h-1 w-1 rounded-[50%] bg-[#707070]"></b>
              <span>
                Your application needs to be approved by the administrator
                before it can be successfully joined, and until then you can
                only access{' '}
                <a
                  className="text-[#0060BF] hover:underline"
                  href="https://www.fs.com"
                  target="_blank"
                >
                  FS.com
                </a>{' '}
                as a personal account.
              </span>
            </li>
            <li className="relative pl-3">
              <b className="absolute left-0 top-[6px] h-1 w-1 rounded-[50%] bg-[#707070]"></b>
              <span>
                When you account (
                <span className="text-[#0060BF]">
                  {userInfo.customerData?.customers_email_address || ''}
                </span>
                ) become an business account, your purchases are shared with
                your teams and you can also access to the shared information of
                your team members (saved carts, quotes, orders, invoices and
                returns). And your historical data will only be visible to
                yourself.
              </span>
            </li>
          </ul>
          <div className="flex justify-end gap-x-4 pt-4">
            <FsButton loading={loading} type="gray" onClick={keepPersonal}>
              Keep the Personal Account
            </FsButton>
            <FsButton loading={loading} onClick={joinBusinessAccount}>
              Join the FS business Account
            </FsButton>
          </div>
        </div>
      </div>
    )
  }
}

export default WorkBenchPage
