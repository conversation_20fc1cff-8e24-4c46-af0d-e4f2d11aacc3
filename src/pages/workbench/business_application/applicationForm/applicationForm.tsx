import { FC, useState, useEffect } from 'react'
import Tips from '@/components/tips/tips'
import {
  CountryItem,
  CountrySelect,
  State,
} from '@/components/ui/CountrySelect'
import apiService from '@/services/api/country'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { PhoneNumber } from '@/components/ui/PhoneNumber'
import FsButton from '@/components/ui/FsButton'
import accountSever from '@/services/api/account'
import message from '@/components/ui/Message'
import { CheckOutlined } from '@ant-design/icons'
import { useRouter } from 'next/router'

// 定义表单数据类型
interface formDataInterface {
  company_name: string
  organization_size: number
  tax_number: string
  company_phone: string
  entry_country_id: number
  city: string
  state: string
  entry_postcode: string
  address_one: string
  address_two: string
  telPrefix: string
  resource_page: string
}

interface ApplicationProps {
  userInfo: { [k: string]: string }
  onJoinSure?: () => void
}

const BusinessApplication: FC<ApplicationProps> = (props) => {
  const router = useRouter()

  const { onJoinSure, userInfo } = props
  const [countryList, setCountryList] = useState<CountryItem[]>([])

  const fetchCountries = async () => {
    const res = await apiService.getCountryList()
    setCountryList(res.data.countryList.source)
    setForm((prev) => ({
      ...prev,
      telPrefix: res.data.tel_prefix,
    }))
  }

  //是否展示提交成功
  const [isSuccess, setSuccess] = useState(false)
  //获取国家列表
  useEffect(() => {
    fetchCountries()
  }, [])

  const [form, setForm] = useState({
    company_name: '',
    organization_size: 0,
    tax_number: '',
    company_phone: '',
    entry_country_id: 223,
    city: '',
    state: '',
    entry_postcode: '',
    address_one: '',
    address_two: '',
    telPrefix: '+1',
    resource_page: '49',
  })

  const [errors, setErrors] = useState<Record<string, string>>({
    company_name: '',
    organization_size: '',
    tax_number: '',
    company_phone: '',
    entry_country_id: '',
    city: '',
    state: '',
    entry_postcode: '',
    address_one: '',
    address_two: '',
    telPrefix: '',
    resource_page: '',
  })

  useEffect(() => {
    if (countryList.length) {
      const [{ states = [] }] = countryList.filter(
        (item) => item.countries_id === form.entry_country_id,
      ) || [{}]
      setStateList(
        states.map((item) => ({ value: item.states_code, label: item.states })),
      )
      setForm((pre) => {
        return { ...pre, state: states[0]?.states_code || '' }
      })
    }
  }, [form.entry_country_id, countryList])

  const [isLoading, setIsLoading] = useState(false)

  const [touched, setTouched] = useState<Record<string, boolean>>({})

  const [stateList, setStateList] = useState<
    { value: string; label: string }[]
  >([])

  //提交事件
  const handleSubmit = async () => {
    const isValid = await validateForm()
    if (isValid) {
      try {
        setIsLoading(true)
        const { code, data } = await accountSever
          .accountApply<formDataInterface>(form)
          .finally(() => {
            setIsLoading(false)
          })
        if (code === 200) {
          setSuccess(true)
          setTimeout(() => {
            // window.location.href = '/workbench/account'
            router.push('/')
          }, 2000)
        }
      } catch (err: any) {
        if (err.code === 400) {
          const { errors } = err
          if (errors && errors.code === 3010 && onJoinSure) {
            onJoinSure()
          }
          message.error(err.message)
        } else {
          console.log(err)
          message.error(err.message)
        }
      }
    }
  }
  //取消事件
  const handleCancel = () => {
    window.location.href = '/'
  }

  //change事件
  const handleChange =
    (name: string) => async (value: string | boolean | number) => {
      setForm((prev) => ({
        ...prev,
        [name]: value,
      }))
      if (touched[name]) {
        const error = await validateFormField(name, value, 'change')
        setErrors((prev) => ({ ...prev, [name]: error }))
      }
    }

  //失焦事件
  const handleBlur = (name: string) => async () => {
    setTouched((prev) => ({ ...prev, [name]: true }))
    const error = await validateFormField(
      name,
      form[name as keyof typeof form],
      'blur',
    )
    setErrors((prev) => ({ ...prev, [name]: error }))
  }

  //表单项校验
  const validateFormField = async (
    name: string,
    value: string | boolean | number,
    triggerType: 'blur' | 'change' | 'submit',
  ) => {
    // 首先进行基础校验
    const basicError = validateField(name, value, triggerType)
    if (basicError) return basicError
    return ''
  }
  //表单校验函数
  const validateField = (
    name: string,
    value: string | boolean | number,
    triggerType: 'blur' | 'change' | 'submit',
  ) => {
    switch (name) {
      case 'company_name': {
        if (!value) return 'Please enter the organization name'
        const trimmedValue = String(value).replace(/\s/g, '')
        if (trimmedValue.length < 2)
          return 'Organization name must be at least 2 characters'
        if (trimmedValue.length > 120)
          return 'Organization name cannot exceed 120 characters'
        return ''
      }
      case 'entry_country_id':
        return !value ? 'Please select the country/region' : ''
      case 'state':
        return !value ? 'Please select the state/province' : ''
      case 'company_phone':
        return !value ? 'Please enter the phone number' : ''
      case 'address_one': {
        if (!value) return 'Please enter the street address'
        const trimmedValue = String(value).replace(/\s/g, '')
        if (trimmedValue.length < 2)
          return 'Street address must be at least 2 characters'
        if (trimmedValue.length > 35)
          return 'Street address cannot exceed 35 characters'
        return ''
      }
      case 'city': {
        if (!value) return 'Please enter the city'
        const trimmedValue = String(value).replace(/\s/g, '')
        if (trimmedValue.length < 2) return 'City must be at least 2 characters'
        if (trimmedValue.length > 40) return 'City cannot exceed 40 characters'
        return ''
      }
      default:
        return ''
    }
  }

  //处理state
  const handleStateListChange = (states: State[]) => {
    if (states.length > 0) {
      setStateList(
        states.map((state) => ({
          value: state.states_code,
          label: state.states,
        })),
      )
      setForm((prev) => ({
        ...prev,
        state: states[0].states_code,
      }))
    }
  }

  const formatAreaCodeOptions = (countries: CountryItem[]) => {
    return countries.map((country) => ({
      value: country.tel_prefix,
      label: `${country.countries_name} (${country.tel_prefix})`,
      searchLabel: `${country.countries_name} ${country.countries_chinese_name} ${country.tel_prefix}`,
    }))
  }

  const validateForm = async () => {
    const newErrors: Record<string, string> = {}
    const validationPromises = Object.keys(form).map(async (key) => {
      const error = await validateFormField(
        key,
        form[key as keyof typeof form],
        'submit',
      )
      if (error) {
        newErrors[key] = error
      }
    })

    await Promise.all(validationPromises)
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  if (isSuccess) {
    return (
      <div className="flex min-h-[calc(100vh-128px)] flex-col items-center justify-center bg-[#FAFAFB]">
        <div className="m-y-auto w-[680px] rounded-lg bg-white p-8">
          <div className="mb-2 flex items-center justify-center gap-x-2">
            <CheckOutlined className="text-2xl font-semibold text-[#10A300]" />{' '}
            <b className="text-xl font-semibold text-[#19191a]">
              We have received your business account application!
            </b>
          </div>
          <div className="text-center text-sm text-[#707070]">
            <p>We will process your application within 5-6 business days. </p>
            <p>
              During the review process, you can use this email and password to
              log in to FS.com and FS Open API Portal. Meanwhile, you can check
              the review progress in Workbench. Thank you for your
              understanding!
            </p>
          </div>
        </div>
      </div>
    )
  }
  return (
    <div className="h-[100%] w-[100%] bg-[#FAFAFB]">
      <div className="container mx-auto flex min-h-[calc(100vh-128px)] bg-[#FAFAFB] py-[64px] max-xl:min-h-[calc(100vh-122px)]">
        <div className="mx-auto my-0 w-[616px] rounded-lg bg-[#fff] p-[32px]">
          <h2 className="mb-[12px] text-base font-semibold text-[#19191A]">
            Certify a free FS business account
          </h2>
          <Tips>
            Hi{' '}
            {userInfo.customers_firstname
              ? userInfo.customers_firstname
              : 'August'}
            , FS Developer Portal APIs is currently available exclusively to FS
            Business Account users. To explore and experience our API services,
            please simply complete your FS Business Account verification first!
          </Tips>
          <form className="mt-3">
            <Input
              label="Organization Name"
              name="company_name"
              value={form.company_name}
              onChange={(e) => handleChange('company_name')(e.target.value)}
              onBlur={handleBlur('company_name')}
              required
              error={errors.company_name}
            />
            <CountrySelect
              label=" Country/Region"
              options={countryList}
              value={form.entry_country_id}
              onChange={(value) => handleChange('country')(value)}
              onBlur={handleBlur('country')}
              onStateListChange={handleStateListChange}
              required
              error={errors.entry_country_id}
              placeholder="Select Country/Region"
            />
            <Input
              label="Street Address"
              name="address_one"
              value={form.address_one}
              onChange={(e) => handleChange('address_one')(e.target.value)}
              onBlur={handleBlur('address_one')}
              placeholder="Apt, Suite, floor, etc."
              required
              error={errors.address_one}
            />
            <Input
              label="Street Address 2"
              name="address2"
              value={form.address_two}
              onChange={(e) => handleChange('address_two')(e.target.value)}
              onBlur={handleBlur('address_two')}
              placeholder="Apt, Suite, floor, etc."
              error={errors.address_two}
            />
            <Input
              label="City"
              name="city"
              value={form.city}
              onChange={(e) => handleChange('city')(e.target.value)}
              onBlur={handleBlur('city')}
              required
              error={errors.city}
            />

            <Select
              label="State/Province"
              options={stateList}
              value={form.state}
              onChange={(value) => handleChange('state')(value)}
              required
              error={errors.state}
            />
            <div className="grid grid-cols-2 gap-x-[20px]">
              <Input
                label="Zip Code"
                name="entry_postcode"
                value={form.entry_postcode}
                onChange={(e) => handleChange('entry_postcode')(e.target.value)}
                onBlur={handleBlur('entry_postcode')}
                error={errors.entry_postcode}
              />
              <PhoneNumber
                label="Phone Number"
                areaCode={form.telPrefix}
                phoneNumber={form.company_phone}
                areaCodeOptions={formatAreaCodeOptions(countryList)}
                onAreaCodeChange={(value) => handleChange('telPrefix')(value)}
                onPhoneNumberChange={(value) =>
                  handleChange('company_phone')(value)
                }
                onBlur={handleBlur('company_phone')}
                required
                error={errors.company_phone}
              />
            </div>
            <div className="btn flex justify-end gap-x-4">
              <FsButton type="white" onClick={handleCancel}>
                Cancel
              </FsButton>
              <FsButton type="red" onClick={handleSubmit} loading={isLoading}>
                Submit
              </FsButton>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default BusinessApplication
