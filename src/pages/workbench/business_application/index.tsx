import { FC, useEffect, useState } from 'react'
import ApplicationForm from './applicationForm/applicationForm'
import JoinAccount from './JoinAccount/joinAccount'
import { BasicInfoInterface } from '../account/index'
import accountApi from '@/services/api/account'
import { useRouter } from 'next/router'

const ApplicationPage: FC = () => {
  const router = useRouter()
  const getHomePageData = async () => {
    const { code, data } = await accountApi.homePage()
    if (code == 200 && data) {
      if (data.type !== 0) {
        router.replace('/workbench/account')
      }
      const { basicInfo } = data
      if (basicInfo) {
        setBasicInfo(basicInfo)
      }
    }
  }
  useEffect(() => {
    getHomePageData()
  }, [])

  const [basicInfo, setBasicInfo] = useState({} as BasicInfoInterface)
  //是否展示加入企业表单
  const [isShowJoin, setShowJoin] = useState(false)
  const handleShowJoin = () => {
    setShowJoin(true)
  }

  return isShowJoin ? (
    <JoinAccount />
  ) : (
    <ApplicationForm onJoinSure={handleShowJoin} userInfo={basicInfo} />
  )
}

export default ApplicationPage
