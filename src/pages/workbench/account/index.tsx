import { FC, Fragment, useEffect, useState } from 'react'
import Card from '../components/Card/Card'
import Layout from '../components/layout'
import accountApi from '@/services/api/account'
import BusinessApplication from './components/businessApplication'
import Credentials, { CredentialsInterface } from './components/credentials'
import { InfoCircleOutlined } from '@ant-design/icons'

import SecretGenerated from './components/secretGenerated'

export interface BasicInfoInterface {
  [key: string]: string
}

export interface OverViewInterface {
  description?: string
  organization_name?: string
  tip?: string
}

export interface ConnectApiItemInterface {
  desc: string
  id: string | number
  summary: string
  typeName: string
  href: string
}

// basicInfo部分
const BasicInfo: FC<{ info: BasicInfoInterface }> = ({ info }) => {
  const basicInfoMap: { key: string; label: string }[] = [
    { key: 'user_name', label: 'User Name' },
    { key: 'email_address', label: 'Email Address' },
    { key: 'organization_name', label: 'Organization Name' },
    { key: 'roles', label: 'Roles' },
    { key: 'customer_manage', label: 'Customer Manager' },
  ]
  return (
    <Card title={<span className="text-[#19191a]">Basic Info</span>}>
      <div className="grid grid-cols-[max-content_auto] gap-x-[212px] gap-y-5">
        {basicInfoMap
          .filter((item) => info[item.key])
          .map((item) => {
            return (
              <Fragment key={item.key}>
                <b className="text-[#19191a]">{item.label}</b>
                <span>{info[item.key]}</span>
              </Fragment>
            )
          })}
      </div>
    </Card>
  )
}

const AccountCenter: FC<{ name: string }> = () => {
  const getHomePageData = async () => {
    const { code, data } = await accountApi.homePage()
    if (code == 200 && data) {
      const { type, credentials, basicInfo, overview, connectApis } = data
      setUserType(type)
      if (type === 0) {
        window.location.href = '/workbench/business_application'
      }
      setCredentialsInfo((pre) => ({ ...pre, ...credentials }))
      serUserInfo((pre) => ({ ...pre, ...basicInfo }))
      if (overview) {
        setOverView((pre) => ({ ...pre, ...overview }))
      }
      if (connectApis) {
        setConnectApis((pre) => [...pre, ...connectApis])
      }
    }
  }

  const [credentialsInfo, setCredentialsInfo] = useState(
    {} as CredentialsInterface,
  )
  const [userInfo, serUserInfo] = useState<BasicInfoInterface>({})

  const [overView, setOverView] = useState<OverViewInterface>({})

  const [connectApis, setConnectApis] = useState<ConnectApiItemInterface[]>([])

  // 用户状态
  const [userType, setUserType] = useState(0)

  useEffect(() => {
    getHomePageData()
  }, [])

  //渲染顶部区域
  const renderPageTop = (type: number) => {
    if (type === 1) {
      return (
        <BusinessApplication
          axis={credentialsInfo.axis}
          detail_link={credentialsInfo.detail_link}
        ></BusinessApplication>
      )
    } else if (type === 2) {
      const { tip = '' } = credentialsInfo
      return (
        <div>
          <p className="mb-4 flex items-center gap-x-3 text-xl text-[#19191a]">
            <b className="font-semibold">Credentials</b>
          </p>
          <div
            className="my-6 flex items-start gap-x-2 px-4 py-[10px]"
            style={{ background: 'rgba(0, 96, 191, 0.04)' }}
          >
            <InfoCircleOutlined className="mt-[3px] text-[#0060BF]" />
            <span className="text-sm text-[#707070]">{tip}</span>
          </div>
        </div>
      )
    } else if ([3, 4].includes(type)) {
      return (
        <Credentials
          info={credentialsInfo}
          type={type}
          overview={overView}
          connectApis={connectApis}
          userInfo={userInfo}
        />
      )
    } else if (type === 5) {
      return (
        <SecretGenerated
          {...credentialsInfo}
          onSuccess={() => {
            getHomePageData()
          }}
        />
      )
    }
  }

  return (
    <Layout>
      <Card>{renderPageTop(userType)}</Card>
      <BasicInfo info={userInfo} />
    </Layout>
  )
}

export default AccountCenter
