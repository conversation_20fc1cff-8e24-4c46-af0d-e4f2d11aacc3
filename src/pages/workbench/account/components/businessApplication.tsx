import { FC } from 'react'
import Progress from './progress'
import { CredentialsInterface } from '../components/credentials'

const BusinessApplication: FC<CredentialsInterface> = (props) => {
  const { detail_link = '', axis } = props

  return (
    <div>
      <div className="">
        <p className="mb-4 flex items-center gap-x-3 text-xl text-[#19191a]">
          <b className="font-semibold">Business Account Application</b>
          <a
            className="text-[13px] font-normal text-[#0060BF] hover:underline"
            href={detail_link}
          >
            View Case Details
          </a>
        </p>
      </div>
      <Progress axis={axis} />
    </div>
  )
}

export default BusinessApplication
