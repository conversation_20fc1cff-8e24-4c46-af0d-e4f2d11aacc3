import { FC, useEffect, useState } from 'react'
import Credentials, { CredentialsInterface } from './credentials'
import FsButton from '@/components/ui/FsButton'
import Tooltip from '@/components/tooltip/tooltip'
import UseIcon from '../../components/Icon/Icon'
import message from '@/components/ui/Message'
import Modal from '@/components/Modal/Modal'
import Tips from '@/components/tips/tips'
import {
  CopyOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
} from '@ant-design/icons'

import noticeApi from '@/services/api/notice'

// 渲染多选框
const renderCheckBox = (checked: boolean) => {
  return checked ? (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="18"
      height="18"
      viewBox="0 0 18 18"
    >
      <g>
        <g>
          <path
            d="M15.6417,18L2.35813,18C1.05685,18,0,16.9436,0,15.6428L0,2.35723C0,1.05645,1.05685,0,2.35813,0L15.6417,0C16.943,0,17.9999,1.05645,17.9999,2.35723L17.9999,15.6445C17.9999,16.9418,16.9395,18,15.6417,18ZM7.38738,13.4851C7.11306,13.4851,6.83873,13.3796,6.62947,13.1722C6.59948,13.1422,6.57163,13.1109,6.54593,13.0784L3.05113,9.80402C2.61854,9.39972,2.59744,8.72296,3.00189,8.28878C3.40634,7.8546,4.08336,7.83527,4.51771,8.23956L7.37245,10.9142L13.4577,4.83135C13.8762,4.41299,14.555,4.41299,14.9735,4.83135C15.392,5.24971,15.392,5.92822,14.9735,6.34658L8.23924,13.08C8.22185,13.1018,8.20349,13.1231,8.18415,13.1439C8.00655,13.3317,7.77872,13.4423,7.54258,13.4738C7.49113,13.4813,7.43926,13.4851,7.38738,13.4851Z"
            fillRule="evenodd"
            fill="#707070"
            fillOpacity="1"
          />
        </g>
      </g>
    </svg>
  ) : (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="18"
      height="18"
      viewBox="0 0 18 18"
    >
      <g>
        <g>
          <path
            d="M2.67891,18L15.3211,18C16.7977,18,18,16.7977,18,15.3211L18,2.67891C18,1.20234,16.7977,0,15.3211,0L2.67891,0C1.20234,0,0,1.20234,0,2.67891L0,15.3211C0,16.7977,1.20234,18,2.67891,18ZM1.28491,15.323L1.28491,2.68086C1.28491,1.9127,1.91069,1.28691,2.67886,1.28516L15.321,1.28516C16.0892,1.28516,16.715,1.91094,16.715,2.6791L16.715,15.323C16.715,16.0912,16.0892,16.717,15.321,16.717L2.67886,16.717C1.91069,16.717,1.28491,16.0912,1.28491,15.323Z"
            fillRule="evenodd"
            fill="#DEE0E3"
            fillOpacity="1"
          />
        </g>
      </g>
    </svg>
  )
}

interface SecretGeneratedInterface extends CredentialsInterface {
  onSuccess: () => void
}

const SecretGenerated: FC<SecretGeneratedInterface> = (credentialsInfo) => {
  const {
    clientStatusString = 'Active',
    timeTip = '',
    allowRefresh = false,
    clientStatus = 1,
    look = 0,
    id = '',
    onSuccess,
  } = credentialsInfo

  const [clientObject, setClientObject] = useState({
    clientId: credentialsInfo.clientId || '',
    clientSecret: credentialsInfo.clientSecret || '',
  })

  const [loading, setLoading] = useState(false)

  //copy
  const handleCopy = async (val: string) => {
    try {
      await window.navigator.clipboard.writeText(val)
      message.success('copy success')
    } catch (error) {
      message.error('fail')
    }
  }

  const [showSecret, setShowSecret] = useState(false)

  // 是否已经生成id和key
  const [hasInitd, setHasInitd] = useState(false)

  useEffect(() => {
    setHasInitd(look === 1)
  }, [look])

  //是否展示id
  const [showClientID, setShowClientID] = useState(false)

  const [showClientKey, setShowClientKey] = useState(false)

  //下载密钥弹窗
  const [showSecretDownModel, setShowSecretDownModel] = useState(false)

  const handleShowDownModel = (val = null) => {
    setShowSecretDownModel(val ? true : !showSecretDownModel)
  }

  const [isAgree, setIsAgree] = useState(false)

  //重新生成密钥
  const [showSecretModel, setShowSecretModel] = useState(false)

  const handelSecretGeneratedClose = async () => {
    if (!isAgree || !id) return
    // 如果是第一次的话就必须要调用更新已读状态的接口
    if (!hasInitd) {
      //发送请求
      setLoading(true)
      const result = await noticeApi.setSecretRead(id).finally(() => {
        setLoading(false)
      })
      if (result.code === 200) {
        message.success(result.message || 'success')
        onSuccess()
      }
    }
    //关闭弹窗
    setShowSecretDownModel(false)
  }

  //重新生成key
  const handleRegenerateAgain = async () => {
    if (!id) return
    setLoading(true)
    const res = await noticeApi.getSecretKeyNew(id).finally(() => {
      setLoading(false)
    })
    if (res.code === 200) {
      message.success(res.message || 'success')
      //关闭刷新token的弹窗
      setShowSecretModel(false)
      //打开token详情的弹窗
      setShowSecretDownModel(true)
      const { clientId = '', clientSecret = '' } = res.data
      setClientObject({ clientId, clientSecret })
    } else {
      message.error(res.message || 'fail')
    }
  }

  //通过id查看密钥
  const getSecretDetail = async () => {
    if (!id) return
    const { code, data } = await noticeApi.getSecretDetail(id)
    if (code === 200 && data) {
      const { clientId = '', clientSecret = '' } = data
      setClientObject({ clientId, clientSecret })
    }
  }

  const [tooltipExpireText, setTooltipExpireText] = useState(
    'This action is available once every 24 hours.',
  )

  useEffect(() => {
    if (clientStatus === 3) {
      setTooltipExpireText(
        'The system will automatically deactivate API credentials if there are no calls made with them within 90 days.',
      )
    }
  }, [clientStatus])

  useEffect(() => {
    if (showSecretDownModel) {
      getSecretDetail()
    }
  }, [showSecretDownModel])

  return (
    <>
      {!hasInitd ? (
        <>
          <Credentials
            info={credentialsInfo}
            customerTitle={
              <b className="font-semibold text-[#19191a]">Credentials</b>
            }
          ></Credentials>
          <FsButton onClick={handleShowDownModel}>View Credentials</FsButton>
        </>
      ) : (
        <div>
          <p className="mb-5 flex items-center gap-x-3 text-xl">
            <b className="font-semibold text-[#19191a]">Credentials</b>
          </p>
          <div className="text-sl grid grid-cols-[150px_auto] gap-x-[100px] gap-y-5">
            <b className="text-[#19191a]">Account Statue</b>
            <span>{clientStatusString}</span>
            <div className="flex items-center">
              <b className="mr-1 text-[#19191a]">Client ID</b>
              <Tooltip text={timeTip || 'The unique Client ID.'}>
                <UseIcon
                  icon="Tips"
                  className="h-4 w-4 text-[16px] text-[#646466]"
                />
              </Tooltip>
            </div>
            <div className="flex items-center gap-x-2">
              {showSecret
                ? clientObject.clientId
                : '******************************'}
              <div
                className="flex h-[28px] w-[28px] cursor-pointer rounded-sm hover:bg-hoverBgRgba"
                onClick={() => handleCopy(clientObject.clientId as string)}
              >
                <CopyOutlined className="m-auto h-4 w-4" />
              </div>
              <div
                className="flex h-[28px] w-[28px] cursor-pointer rounded-sm hover:bg-hoverBgRgba"
                onClick={() => {
                  setShowSecret(!showSecret)
                }}
              >
                {showSecret ? (
                  <EyeOutlined className="m-auto h-4 w-4" />
                ) : (
                  <EyeInvisibleOutlined className="m-auto h-4 w-4" />
                )}
              </div>
              <div className="ml-3">
                <Tooltip text={tooltipExpireText}>
                  <FsButton
                    disabled={!allowRefresh}
                    onClick={() => setShowSecretModel(true)}
                  >
                    Regenerate
                  </FsButton>
                </Tooltip>
              </div>
            </div>
          </div>
          <Modal
            show={showSecretModel}
            className="z-10 w-[480px] px-6 pb-5 pt-4 text-[#19191a]"
            title="Regenerate Client ID and Secret"
            onClose={() => {
              setShowSecretModel(false)
            }}
          >
            <div className="border-t border-[#E5E5E5] pt-4">
              <h3 className="mb-4 text-sm leading-[22px] text-[#707070]">
                Regenerating your client ID will disable your old client ID and
                secret. We cannot recover any previously generated client ID or
                secrets.
              </h3>
              <div className="flex items-center gap-x-2 bg-bgRgba2 px-4 py-[10px] text-[#c00]">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  version="1.1"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                >
                  <g>
                    <g>
                      <path
                        d="M8.00001701171875,14.8333259765625C11.77398701171875,14.8333259765625,14.83338701171875,11.7739259765625,14.83338701171875,7.9999559765625C14.83338701171875,4.2260159765625005,11.77398701171875,1.1666259765625,8.00001701171875,1.1666259765625C4.2260770117187505,1.1666259765625,1.16668701171875,4.2260159765625005,1.16668701171875,7.9999559765625C1.16668701171875,11.7739259765625,4.2260770117187505,14.8333259765625,8.00001701171875,14.8333259765625ZM8.00001701171875,2.1666259765625C11.22168701171875,2.1666259765625,13.83338701171875,4.7782959765625,13.83338701171875,7.9999559765625C13.83338701171875,11.2216259765625,11.22168701171875,13.8333259765625,8.00001701171875,13.8333259765625C4.77835701171875,13.8333259765625,2.16668701171875,11.2216259765625,2.16668701171875,7.9999559765625C2.16668701171875,4.7782959765625,4.77835701171875,2.1666259765625,8.00001701171875,2.1666259765625ZM7.50018701171875,9.6667059765625L7.50018701171875,4.3333759765625L8.50018701171875,4.3333759765625L8.50018701171875,9.6667059765625L7.50018701171875,9.6667059765625ZM8.00025701171875,11.6667259765625C8.36844701171875,11.6667259765625,8.66691701171875,11.3682259765625,8.66691701171875,11.0000459765625C8.66691701171875,10.6318559765625,8.36844701171875,10.3333759765625,8.00025701171875,10.3333759765625C7.63206701171875,10.3333759765625,7.33358701171875,10.6318559765625,7.33358701171875,11.0000459765625C7.33358701171875,11.3682259765625,7.63206701171875,11.6667259765625,8.00025701171875,11.6667259765625Z"
                        fillRule="evenodd"
                        fill="#C00000"
                        fillOpacity="1"
                      />
                    </g>
                  </g>
                </svg>
                <span>This action is available once every 24 hours.</span>
              </div>
              <div className="flex justify-end gap-x-4 pt-4">
                <FsButton
                  type="white"
                  onClick={() => {
                    setShowSecretModel(false)
                  }}
                >
                  Cancel
                </FsButton>
                <FsButton loading={loading} onClick={handleRegenerateAgain}>
                  Regenerate
                </FsButton>
              </div>
            </div>
          </Modal>
        </div>
      )}
      {/* 密钥详情弹窗 */}
      <Modal
        show={showSecretDownModel}
        showCloseIcon={false}
        title="Client ID and Secret Generated"
        className="z-10 w-[480px] px-6 pb-5 pt-4 text-[#19191a]"
        onClose={handleShowDownModel}
      >
        <div className="border-t border-[#E5E5E5] pt-4">
          <p className="mb-4">
            Copy your secret and keep it in a safe place. You can return and
            view your access key at any time but your secret is only available
            once.
          </p>
          <Tips className="mb-4">
            Copy your secret before closing the dialog box. If you close
            vwithout copying the secret, you will need to generate a new one.
          </Tips>
          <div className="text-[#707070]">
            <div>
              <p className="mb-1 text-xs leading-5">Client ID</p>
              <div className="mb-4 flex h-[42px] items-center gap-x-2 bg-[#F6F6F8] px-3">
                <span className="flex-1">
                  {showClientID
                    ? clientObject.clientId
                    : '************************************'}
                </span>
                <div
                  className="flex h-[28px] w-[28px] cursor-pointer rounded-sm hover:bg-hoverBgRgba"
                  onClick={() => handleCopy(clientObject.clientId as string)}
                >
                  <CopyOutlined className="m-auto h-4 w-4" />
                </div>
                <div
                  className="flex h-[28px] w-[28px] cursor-pointer rounded-sm hover:bg-hoverBgRgba"
                  onClick={() => {
                    setShowClientID(!showClientID)
                  }}
                >
                  {showClientID ? (
                    <EyeOutlined className="m-auto h-4 w-4" />
                  ) : (
                    <EyeInvisibleOutlined className="m-auto h-4 w-4" />
                  )}
                </div>
              </div>
            </div>
            <div>
              <p className="mb-1 text-xs leading-5">Client Secret</p>
              <div className="mb-4 flex h-[42px] items-center gap-x-2 bg-[#F6F6F8] px-3">
                <span className="flex-1">
                  {showClientKey
                    ? clientObject.clientSecret
                    : '************************************'}
                </span>
                <div
                  className="flex h-[28px] w-[28px] cursor-pointer rounded-sm hover:bg-hoverBgRgba"
                  onClick={() =>
                    handleCopy(clientObject.clientSecret as string)
                  }
                >
                  <CopyOutlined className="m-auto h-4 w-4" />
                </div>
                <div
                  className="flex h-[28px] w-[28px] cursor-pointer rounded-sm hover:bg-hoverBgRgba"
                  onClick={() => {
                    setShowClientKey(!showClientKey)
                  }}
                >
                  {showClientKey ? (
                    <EyeOutlined className="m-auto h-4 w-4" />
                  ) : (
                    <EyeInvisibleOutlined className="m-auto h-4 w-4" />
                  )}
                </div>
              </div>
            </div>
            <div>
              <p className="mb-2 text-sm leading-5 text-[#c00]">* Required</p>
              <div
                className="flex items-start gap-x-2"
                onClick={() => {
                  setIsAgree(!isAgree)
                }}
              >
                {renderCheckBox(isAgree)}
                <p>
                  <span className="text-[#c00]">*</span> I acknowledge that I
                  have stored the secret in a safe place, and if lost, I will
                  need to generate a new access key and secret.
                </p>
              </div>
            </div>
            <div className="flex justify-end pt-4">
              <FsButton
                disabled={!isAgree}
                loading={loading}
                onClick={handelSecretGeneratedClose}
              >
                Close
              </FsButton>
            </div>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default SecretGenerated
