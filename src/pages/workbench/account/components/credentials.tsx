import { FC, ReactNode, useState } from 'react'
import { InfoCircleOutlined } from '@ant-design/icons'
import FsButton from '@/components/ui/FsButton'
import Progress from './progress'
import ApplyCredentials from './applyCredentials'

import { ConnectApiItemInterface } from '../index'

import { OverViewInterface, BasicInfoInterface } from '../index'

export interface CredentialsInterface {
  status?: number //流程轴 status  1.(已提交)审核中 2.审核成功 3。审核失败
  detail_link?: string
  case_number?: string
  type?: number
  tip?: string //提示语
  axis: Array<{ status: boolean; date: string; name: string }>
  clientId?: string
  clientSecret?: string
  clientStatusString?: string
  expireTime?: string | null
  issueTime?: string | null
  allowRefresh?: boolean //是否运行重新刷新
  timeTip?: string
  look?: 0 | 1 // "type": 5 情况下 新增字段look  look=0, 展示流程轴+按钮 look=1展示凭证
  id?: string
  clientStatus?: number
}

interface CredentialsProps {
  info: CredentialsInterface
  overview?: OverViewInterface
  connectApis?: ConnectApiItemInterface[]
  type?: number
  userInfo?: BasicInfoInterface
  customerTitle?: ReactNode
}

const Credentials: FC<CredentialsProps> = ({
  info,
  overview,
  connectApis = [],
  type = 0,
  userInfo = {},
  customerTitle,
}) => {
  const {
    axis,
    detail_link = '/',
    tip = '',
  } = info || ({} as CredentialsInterface)

  // 是否展示申请表单
  const [showApplicationForm, setShowApplicationForm] = useState(false)

  //申请
  const handelApplyCred = () => {
    setShowApplicationForm(true)
  }

  const renderContent = (type: number) => {
    if (type === 3) {
      if (showApplicationForm) {
        return (
          <div>
            <div>
              <p className="mb-4 flex items-center gap-x-3 text-xl text-[#19191a]">
                <b className="font-semibold">Credentials</b>
              </p>
            </div>
            <ApplyCredentials userInfo={userInfo} />
          </div>
        )
      } else {
        return (
          <div>
            <div>
              <p className="mb-4 flex items-center gap-x-3 text-xl text-[#19191a]">
                <b className="font-semibold">Credentials</b>
              </p>
            </div>
            <div>
              <FsButton onClick={handelApplyCred}>
                Apply for Credentials
              </FsButton>
            </div>
          </div>
        )
      }
    } else if (type == 4 && overview) {
      const { description, organization_name, tip } = overview
      return (
        <div>
          <div>
            <p className="mb-4 flex items-center gap-x-3 text-xl text-[#19191a]">
              <b className="font-semibold">Credentials</b>
              <a
                target="_blank"
                className="text-[13px] text-[#0060BF] hover:underline"
                href={detail_link}
              >
                View Case Details
              </a>
            </p>
          </div>
          <Progress axis={axis} />
          <div className="mt-6">
            <h3 className="text-base font-semibold text-[#19191a]">Overview</h3>
            <div
              className="my-6 flex items-start gap-x-2 px-4 py-[10px]"
              style={{ background: 'rgba(0, 96, 191, 0.04)' }}
            >
              <InfoCircleOutlined className="mt-[3px] text-[#0060BF]" />
              <span className="text-sm text-[#707070]">{tip}</span>
            </div>
            <div className="flex flex-col gap-y-[20px] text-[13px] text-[#19191a]">
              <div>
                <p className="mb-1 text-xs">Organization Name</p>
                <div className="min-h-[42px] break-all rounded-sm bg-[#F6F6F8] px-3 py-[12px]">
                  {organization_name}
                </div>
              </div>
              <div>
                <p className="mb-1 text-xs">
                  Description of how you will use the API(s) (optional)
                </p>
                <div className="min-h-[42px] break-all rounded-sm bg-[#F6F6F8] px-3 py-[12px]">
                  {description}
                </div>
              </div>
            </div>
          </div>
          <div className="mt-6">
            <h3 className="text-base font-semibold text-[#19191a]">
              Connect APIs
            </h3>
            <ul className="mt-5 grid grid-cols-4 gap-5">
              {connectApis?.map((item) => {
                return (
                  <li
                    key={item.id}
                    className="flex flex-col rounded-lg border border-[#E5E5E5] bg-white p-5"
                  >
                    <h4 className="text-sm font-semibold text-[#19191a]">
                      {item.typeName}
                    </h4>
                    <p className="my-2 text-[13px] leading-[20px]">
                      {item.desc}
                    </p>
                    <a
                      href={`/docs/${item.id}` || '/'}
                      target="_blank"
                      className="mt-auto cursor-pointer text-[13px] leading-[20px] text-[#0060BF] hover:underline"
                    >
                      Read API Document
                    </a>
                  </li>
                )
              })}
            </ul>
          </div>
        </div>
      )
    } else {
      return (
        <div>
          <div className="">
            <p className="mb-4 flex items-center gap-x-3 text-xl">
              {customerTitle ? (
                customerTitle
              ) : (
                <>
                  <b className="font-semibold text-[#19191a]">Credentials</b>
                  <a className="text-[13px] font-normal" href={detail_link}>
                    View Case Details
                  </a>
                </>
              )}
            </p>
          </div>
          <Progress axis={axis} />
          <div>
            {tip ? (
              <div
                className="my-6 flex items-start gap-x-2 px-4 py-[10px]"
                style={{ background: 'rgba(0, 96, 191, 0.04)' }}
              >
                <InfoCircleOutlined className="mt-[3px] text-[#0060BF]" />
                <span className="text-sm text-[#707070]">{tip}</span>
              </div>
            ) : null}
          </div>
        </div>
      )
    }
  }

  return renderContent(type)
}

export default Credentials
