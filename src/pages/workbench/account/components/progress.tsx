import { FC } from 'react'
import { CheckCircleFilled } from '@ant-design/icons'
import classNames from 'classnames'

export interface CredentialsInterface {
  status?: number
  axis: Array<{ status: boolean; date: string; name: string }>
}

const Progress: FC<{ axis: CredentialsInterface['axis'] }> = ({
  axis = [],
}) => {
  if (!axis.length) {
    return null
  }
  const axiosMap = axis.map((item, index, arr) => {
    let className = 'bg-[#ccc]'
    let showIcon = false
    if (item.status) {
      className = 'bg-[#10A300] bg-opacity-50'
      showIcon = true
    } else {
      className = arr[index - 1].status ? 'bg-[#10A300]' : 'bg-[#ccc]'
      showIcon = arr[index - 1].status
    }
    return { ...item, className, showIcon }
  })

  return (
    <div>
      <ul className="flex gap-x-1">
        {axiosMap.map((item, index) => {
          return (
            <li key={index} className="flex-1">
              <p className={classNames('mb-4 h-[6px]', item.className)}></p>
              <div className="flex items-center gap-x-2">
                {item.showIcon ? (
                  <CheckCircleFilled className="text-xl text-[#10A300]" />
                ) : null}
                <span
                  className={`text-sm ${item.showIcon ? 'font-semibold' : ''} ${!item.status && item.showIcon ? 'text-[#19191a]' : ''}`}
                >
                  {item.name}
                </span>
                <span className="text-xs">{item.date}</span>
              </div>
            </li>
          )
        })}
      </ul>
    </div>
  )
}

export default Progress
