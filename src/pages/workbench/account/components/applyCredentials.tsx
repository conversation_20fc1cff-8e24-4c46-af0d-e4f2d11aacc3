import { FC, useEffect, useState } from 'react'
import {
  InfoCircleOutlined,
  CheckSquareFilled,
  BorderOutlined,
} from '@ant-design/icons'
import { Input } from '@/components/ui/Input'
import accountApi from '@/services/api/account'
import noticeApi from '@/services/api/notice'
import classNames from 'classnames'
import FsButton from '@/components/ui/FsButton'
import message from '@/components/ui/Message'
import Modal from '@/components/Modal/Modal'
import { useRouter } from 'next/router'
import { BasicInfoInterface } from '../index'
import { version } from '@/docs/conditions.mdx'

interface ApiListItem {
  id: string
  typeName: string
  desc: string
  summary: string
  isSub?: boolean
}

const ApplyCredentials: FC<{ userInfo: BasicInfoInterface }> = (props) => {
  const { userInfo = {} } = props
  const router = useRouter()
  const [formData, setFormData] = useState({
    organization_name: userInfo.organization_name || '',
    description: '',
    connect_apis: '',
    agreePolicy: false,
  })

  const [errors, setErrors] = useState<Record<string, string>>({
    organization_name: '',
    description: '',
    connect_apis: '',
    agreePolicy: '',
  })

  const [touched, setTouched] = useState<Record<string, boolean>>({})

  const [loading, setLoading] = useState(false)

  const handlePolicyAgree = () => {
    setFormData((pre) => ({ ...pre, agreePolicy: !pre.agreePolicy }))
    handleBlur('agreePolicy')
  }

  const changeInput =
    (name: string) => async (value: string | boolean | number) => {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }))
      if (touched[name]) {
        const error = await validateForm(name, value)
        setErrors((prev) => ({ ...prev, [name]: error }))
      }
    }

  //失去焦点事件
  const handleBlur = (name: string) => async () => {
    setTouched((prev) => ({ ...prev, [name]: true }))
    const error = await validateForm(
      name,
      formData[name as keyof typeof formData],
    )
    setErrors((prev) => ({ ...prev, [name]: error }))
  }

  const validateForm = (name: string, value: string | boolean | number) => {
    switch (name) {
      case 'organization_name':
        if (!value) return 'Please enter the organization name'
        const trimmedValue = String(value).replace(/\s/g, '')
        if (trimmedValue.length < 2)
          return 'Organization name must be at least 2 characters'
        if (trimmedValue.length > 120)
          return 'Organization name cannot exceed 120 characters'
        return ''
      case 'description':
        const trimmedDesc = String(value).replace(/\s/g, '')
        if (trimmedDesc.length < 2 && trimmedDesc.length > 0) {
          return 'Description must be at least 2 characters'
        }
        if (trimmedDesc.length > 1000) {
          return 'Description cannot exceed 1000 characters'
        }
        return ''
      case 'agreePolicy':
        return !value ? 'Please agree the policy' : ''
      default:
        return ''
    }
  }

  const validateAll = async () => {
    const newErrors: Record<string, string> = {}
    const validationPromises = Object.keys(formData).map(async (key) => {
      const error = await validateForm(
        key,
        formData[key as keyof typeof formData],
      )
      if (error) {
        newErrors[key] = error
      }
    })

    await Promise.all(validationPromises)
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 获取api列表
  const getApiList = async () => {
    const { code, data } = await noticeApi.getApiList()
    if (code == 200 && data) {
      const list = data.map((item: ApiListItem) => ({ ...item, isSub: false }))
      setApiList([...list])
    }
  }

  useEffect(() => {
    getApiList()
  }, [])

  //api列表
  const [apiList, setApiList] = useState<ApiListItem[]>([])

  //订阅列表
  const [subsList, setSubsList] = useState<ApiListItem[]>([])

  const [apiAddedTotal, setApiAddedTotal] = useState(0)

  const handelSubs = (item: ApiListItem) => {
    setApiList((pre) =>
      pre.map((i) => {
        return i.id === item.id ? { ...i, isSub: !i.isSub } : i
      }),
    )
  }

  useEffect(() => {
    const arr = apiList.filter((item) => item.isSub)
    setSubsList(arr)
    setApiAddedTotal(arr.length)
    const connect_apis = arr.map((item) => item.id).join(',')
    setFormData((pre) => ({ ...pre, connect_apis }))
  }, [apiList])

  //提交
  const handleSubmit = async () => {
    // 校验表单
    const isValid = await validateAll()
    if (isValid) {
      if (!formData.connect_apis.length) {
        message.error('Please select at least one API.')
        return
      }
      try {
        setLoading(true)
        const { code, message: msg } = await accountApi
          .applySecret({ ...formData, version })
          .finally(() => setLoading(false))
        if (code === 200) {
          setSuccess(true)
        } else {
          message.error(msg || '')
        }
      } catch (error: any) {
        console.log(error, 'errrrrr')
        message.error(error.message || '')
      }
    }
  }

  const [isSuccess, setSuccess] = useState(false)

  const handleClose = () => {
    setSuccess(false)
    router.reload()
  }

  return (
    <>
      <div>
        <h2 className="mt-6 text-base font-semibold text-[#19191a]">
          Apply for Credentials
        </h2>
        <div
          className="my-5 flex items-start gap-x-2 px-4 py-[10px]"
          style={{ background: 'rgba(0, 96, 191, 0.04)' }}
        >
          <InfoCircleOutlined className="mt-[3px] text-[#0060BF]" />
          <span className="text-sm text-[#707070]">
            After submitting your credential application, FS developer team will
            review and configure your request. This process typically takes 5-6
            business days. If you have any questions, please contact your
            account manager.{' '}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-x-5">
          <Input
            label="Organization Name"
            disabled={!!userInfo.organization_name}
            required
            value={formData.organization_name}
            onBlur={handleBlur('organization_name')}
            onChange={(e) => changeInput('organization_name')(e.target.value)}
            error={errors.organization_name}
          />
          <Input
            label="Description of how you will use the API(s) (optional)"
            value={formData.description}
            maxLength={1001}
            onBlur={handleBlur('description')}
            onChange={(e) => changeInput('description')(e.target.value)}
            error={errors.description}
          />
        </div>
      </div>
      <h2 className="mt-6 text-base font-semibold text-[#19191a]">
        Connect APIs
      </h2>
      <div
        className="my-5 flex items-start gap-x-2 px-4 py-[10px]"
        style={{ background: 'rgba(0, 96, 191, 0.04)' }}
      >
        <InfoCircleOutlined className="mt-[3px] text-[#0060BF]" />
        <span className="text-sm text-[#707070]">
          We have filtered available APIs, please must select at least one API.
        </span>
      </div>
      {/* table */}
      <div>
        <div>
          <div
            className="grid grid-cols-[240px_428px_auto] rounded-t-lg"
            style={{ background: ' rgba(64, 128, 255, 0.18)' }}
          >
            <div className="rounded-tl-lg border border-r-0 border-table-header-rgba px-3 py-[10px]">
              API Name
            </div>
            <div className="border border-r-0 border-table-header-rgba px-3 py-[10px]">
              Description
            </div>
            <div className="rounded-tr-lg border border-table-header-rgba px-3 py-[10px]">
              Product Line
            </div>
          </div>
          {apiList.map((item, index, arr) => {
            return (
              <div className="grid grid-cols-[240px_428px_auto]" key={item.id}>
                <div
                  className={classNames(
                    'border border-r-0 border-t-0 border-[#E5E5E5] px-3 py-[10px]',
                    { 'rounded-bl-lg': index + 1 === arr.length },
                  )}
                >
                  {item.summary}
                </div>
                <div className="border border-r-0 border-t-0 border-[#E5E5E5] px-3 py-[10px]">
                  {item.desc}
                </div>
                <div
                  className={classNames(
                    'border border-t-0 border-[#E5E5E5] px-3 py-[10px]',
                    { 'rounded-br-lg': index + 1 === arr.length },
                  )}
                >
                  <div className="flex justify-between">
                    <span>{item.typeName}</span>
                    <div
                      className="flex flex-[0_0_150px] cursor-pointer gap-x-2 text-[13px]"
                      onClick={() => handelSubs(item)}
                    >
                      <div
                        className={classNames(
                          'flex h-4 w-[28px] items-center rounded-[8px] p-[2px]',
                          item.isSub ? 'bg-[#10A300]' : 'bg-[#ccc]',
                        )}
                      >
                        <div
                          className={classNames(
                            'h-3 w-3 rounded-[6px] bg-white',
                            { 'ml-auto': item.isSub },
                          )}
                        ></div>
                      </div>
                      <span
                        className={classNames(
                          item.isSub ? 'text-[#10A300]' : 'text-[#0060BF]',
                          'hover:underline',
                        )}
                      >
                        {item.isSub ? 'Added' : 'Subscription'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
      {/* 卡片区域 */}
      <div className="mt-6">
        <h2 className="text-base font-semibold text-[#19191a]">
          APls added to subscription ({apiAddedTotal})
        </h2>
        <ul className="mt-5 grid grid-cols-4 gap-5">
          {subsList.map((item) => {
            return (
              <li
                key={item.id}
                className="rounded-lg border border-[#E5E5E5] p-5 text-sm"
              >
                <h2 className="mb-2 font-semibold">{item.summary}</h2>
                <p className="mb-2 text-[13px]">{item.desc}</p>
                <div>
                  <span
                    onClick={() => handelSubs(item)}
                    className="cursor-pointer text-[#0060BF] hover:underline"
                  >
                    Remove
                  </span>
                </div>
              </li>
            )
          })}
        </ul>
      </div>
      {/* pilicy */}
      <div className="flex items-center gap-x-2 py-5">
        <span
          className="cursor-pointer text-[18px]"
          onClick={handlePolicyAgree}
        >
          {formData.agreePolicy ? (
            <CheckSquareFilled />
          ) : (
            <BorderOutlined
              className={errors.agreePolicy.length ? 'text-[#c00]' : ''}
            />
          )}
        </span>
        <span>
          By checking this box, I acknowledge that I have read and accepthe{' '}
          <a
            target="_blank"
            href={'/guides/conditions'}
            className="cursor-pointer text-[#0060BF] hover:underline"
          >
            FS API Terms and Conditions
          </a>
          .*
        </span>
      </div>
      <FsButton onClick={handleSubmit} loading={loading}>
        Submit
      </FsButton>
      <Modal
        show={isSuccess}
        title="Pending approval"
        className="w-[480px] px-6 pb-4 pt-[13px]"
        onClose={handleClose}
      >
        <p className="pt-4">
          Your subscription requires approval from the FS developer teams. This
          process typically takes 5-6 business days, you can check the
          application progress in the Credentials section.
        </p>
      </Modal>
    </>
  )
}

export default ApplyCredentials
