import { FC, useEffect, useState } from 'react'
import Layout from '../components/layout'
import Card from '../components/Card/Card'
import { Select, SelectOption } from '@/components/ui/Select'
import { Input } from '@/components/ui/Input'
import FsButton from '@/components/ui/FsButton'
import noticeApi from '@/services/api/notice'
import classNames from 'classnames'
import { Pagination, PaginationProps } from '@/components'
import { CaretUpOutlined } from '@ant-design/icons'
import DataPicker from '@/components/dataPicker/DataPicker'
import message from '@/components/ui/Message'
import dayjs from 'dayjs'
import { useRouter } from 'next/router'

interface noticeConfigOption {
  customerId: string
  search: string
  read: string
  type: string
  startTime: string
  endTime: string
}

interface TagData {
  name?: string
  type: number
}
interface NoticeListItem {
  id: number
  content: string
  state: string
  sendTime: string
  read: number
  title: string
  type: 0 | 1 //0 changelog 1 message
}

interface GetListParams extends noticeConfigOption {
  page: number
  pageSize: number
}

const NotificationsPage: FC = () => {
  const typeOption: SelectOption[] = [
    { value: '3', label: 'ALL' },
    { value: '0', label: 'ChangeLog' },
    { value: '1', label: 'Message' },
  ]
  const stateOption: SelectOption[] = [
    { value: '3', label: 'ALL' },
    { value: '1', label: 'Read' },
    { value: '0', label: 'Unread' },
  ]

  const [noticeList, setNoticeList] = useState<NoticeListItem[]>([])

  const [noticeConfig, setNoticeConfig] = useState<noticeConfigOption>({
    customerId: '',
    search: '',
    read: '3',
    type: '3',
    startTime: '',
    endTime: '',
  })

  const [loading, setLoading] = useState(false)

  const handlePageChange = (page: number, pageSize: number) => {
    setPageConfig((pre) => {
      return { ...pre, current: page, pageSize }
    })
    handleSearch()
  }

  const handleShowChange = (current: number, size: number) => {
    setPageConfig((pre) => {
      return { ...pre, current, pageSize: size }
    })
    handleSearch()
  }

  const [pageConfig, setPageConfig] = useState<PaginationProps>({
    current: 1,
    total: 0,
    defaultPageSize: 10,
    pageSize: 10,
    showQuickJumper: true,
  })

  const handleDateChange = (val: { endTime: string; startTime: string }) => {
    const { startTime, endTime } = val
    console.log(startTime, endTime, 'handleDateChange')

    setNoticeConfig((pre) => {
      return { ...pre, startTime, endTime }
    })
  }

  // 处理change
  const handleChange = (key: string, val: string) => {
    setNoticeConfig((pre) => {
      return { ...pre, [key as keyof typeof noticeConfig]: val }
    })
  }

  const handleSearch = () => {
    getNoticeList()
  }

  const handleReset = () => {
    const params = {
      customerId: '',
      search: '',
      read: '3',
      type: '3',
      startTime: '',
      endTime: '',
    }
    setNoticeConfig(params)
    getNoticeList({ ...params, read: '', type: '' })
  }

  const handelSelect = (key: 'type' | 'read', value: string) => {
    setNoticeConfig((pre) => {
      return { ...pre, [key as keyof typeof noticeConfig]: value }
    })
  }

  const router = useRouter()

  //表格展示的行数
  const [activeLine, setActiveLine] = useState<number>(0)

  const getTimeString = (time: string) => {
    if (time) {
      const result = dayjs(time).format('YYYY-MM-DD') + ' 00:00:00'
      return result
    } else {
      return ''
    }
  }

  //获取消息列表
  const getNoticeList = async (p: { [key: string]: string } = {}) => {
    const params: GetListParams = {
      ...noticeConfig,
      startTime: getTimeString(noticeConfig.startTime),
      endTime: getTimeString(noticeConfig.endTime),
      type: noticeConfig.type === '3' ? '' : noticeConfig.type,
      read: noticeConfig.read === '3' ? '' : noticeConfig.read,
      page: pageConfig.current,
      pageSize: pageConfig.pageSize || 10,
    }
    const { id = '' } = router.query
    if (id) {
      setActiveLine(Number(id))
    }

    setLoading(true)
    try {
      const { code, data } = await noticeApi
        .getNoticeList<GetListParams>({ ...params, ...p })
        .finally(() => {
          setLoading(false)
        })
      if (code === 200 && data) {
        const { records = [], total = 0 } = data
        setPageConfig((pre) => ({ ...pre, total }))
        setNoticeList(records)
      }
    } catch (error) {
      console.log(error)
      message.error('fail')
    }
  }

  const handleItemActive = (item: NoticeListItem) => {
    setActiveLine(activeLine === item.id ? 0 : item.id)
    if (String(item.read) !== '1') {
      setMessageReade(item.id)
    }
  }

  const setMessageReade = async (id: number) => {
    try {
      const { code } = await noticeApi.updateNoticeStatus(id)
      console.log(code)
      if (code === 200) {
        //2.修改本地数据
        const arr = noticeList.map((item) => {
          if (item.id === id) {
            return { ...item, read: 1 }
          }
          return item
        })
        setNoticeList(arr as NoticeListItem[])
      }
    } catch (error: any) {
      message.error((error.message as string) || 'fail')
    }
  }

  useEffect(() => {
    getNoticeList()
  }, [])

  //渲染标签
  const renderTag = (tag: TagData) => {
    let colorValue = ''
    switch (tag.type) {
      case 0:
        colorValue = 'bg-[#ECDCF9] text-[#512971]'
        break
      case 1:
        colorValue = 'bg-[#D6F5DF] text-[#09832A]'
        break
      default:
        colorValue = 'bg-[#ECDCF9] text-[#512971]'
        break
    }

    enum tagMap {
      'ChangLog' = 0,
      'Message' = 1,
    }

    return (
      <div
        className={classNames(
          colorValue,
          'inline-block h-[max-content] w-[max-content] rounded-sm px-1 text-xs font-semibold leading-[20px]',
        )}
      >
        {tagMap[tag.type]}
      </div>
    )
  }

  return (
    <Layout>
      <Card title="Notifications">
        <p>
          Receive and view system message notifications from the FS Developer
          Portal.
        </p>
      </Card>
      <Card>
        {/* 选择 */}
        {/* <div className="grid grid-cols-5 items-center gap-x-5"> */}
        <div className="flex flex-wrap items-end gap-x-5">
          <div className="flex-[0_0_290px]">
            <Select
              label="Message type"
              placeholder="Select"
              value={noticeConfig.type}
              options={typeOption}
              showSearch={false}
              trigger="hover"
              onChange={(val) => handelSelect('type', val as string)}
            ></Select>
          </div>
          <div className="flex-[0_0_290px]">
            <Select
              label="State"
              placeholder="Select"
              value={noticeConfig.read}
              showSearch={false}
              options={stateOption}
              trigger="hover"
              onChange={(val) => handelSelect('read', val as string)}
            ></Select>
          </div>
          <div className="flex-[0_0_290px]">
            <DataPicker
              label="Sending time"
              onChange={handleDateChange}
              startTime={noticeConfig.startTime}
              endTime={noticeConfig.endTime}
              placeholder="Select By ..."
            />
          </div>
          <div className="flex-[0_0_290px]">
            <Input
              label="Content"
              value={noticeConfig.search}
              onChange={(e) => handleChange('search', e.target.value)}
              placeholder="Input"
            />
          </div>
          <div className="flex h-[100%] items-end gap-x-3 pb-4">
            <FsButton onClick={handleSearch} loading={loading}>
              Search
            </FsButton>
            <FsButton type="grayline" onClick={handleReset}>
              Reset
            </FsButton>
          </div>
        </div>
        {!noticeList.length ? (
          <div className="flex h-[266px] items-center justify-center">
            <p className="color-[#19191a] text-base font-semibold">{`You don't have any notifications right now.`}</p>
          </div>
        ) : null}
        {noticeList.length ? (
          <div className="mb-5 table w-[100%]">
            <div>
              <div>
                <div
                  className="grid grid-cols-[auto_300px_448px] rounded-t-lg font-semibold text-[#19191a]"
                  style={{ background: ' rgba(64, 128, 255, 0.18)' }}
                >
                  <div className="rounded-tl-lg px-3 py-[10px]">Content</div>
                  <div className="px-3 py-[10px]">State</div>
                  <div className="rounded-tr-lg px-3 py-[10px]">
                    Sending Time
                  </div>
                </div>
                {noticeList.map((item, index, arr) => {
                  return (
                    <div key={item.id}>
                      {/* 表格内容区 */}
                      <div
                        className="grid cursor-pointer grid-cols-[auto_300px_448px] rounded-bl-lg rounded-br-lg border-t border-[#E5E5E5] hover:bg-hoverBgRgba"
                        onClick={() => handleItemActive(item)}
                      >
                        <div
                          className={classNames(
                            'flex items-center gap-x-1 py-[10px] pr-3',
                          )}
                        >
                          <CaretUpOutlined
                            rotate={activeLine !== item.id ? 90 : 180}
                            className="h-[max-content] cursor-pointer"
                          />
                          {renderTag({ type: item.type })}
                          <span>{item.content}</span>
                        </div>
                        <div className="px-3 py-[10px]">
                          {item.read === 1 ? 'Read' : 'Unread'}
                        </div>
                        <div
                          className={classNames('px-3 py-[10px]', {
                            'rounded-br-lg': index + 1 === arr.length,
                          })}
                        >
                          {item.sendTime}
                        </div>
                      </div>
                      <div
                        className={classNames({
                          hidden: activeLine !== item.id,
                        })}
                      >
                        <div className="mb-[10px] ml-4 rounded-lg bg-bgRgba1 p-3">
                          <h2 className="mb-3 flex items-center gap-x-1">
                            <b>{item.title}</b>
                            {renderTag({
                              type: item.type,
                            })}
                          </h2>
                          <div className="mb-5">
                            <p>{item.sendTime}</p>
                          </div>
                          <p className="text-[#19191a]">{item.content}</p>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        ) : null}
        <Pagination
          {...pageConfig}
          onChange={handlePageChange}
          onShowSizeChange={handleShowChange}
        />
      </Card>
    </Layout>
  )
}

export default NotificationsPage
