import { FC, ReactNode } from 'react'
import AccountAside, { AsideItemInterface } from './AccountAside/AccountAside'

interface layoutProps {
  children: ReactNode
}
const asideData: AsideItemInterface[] = [
  {
    id: '1',
    icon: 'UserIcon',
    activeIcon: 'UserIconActive',
    title: 'Credentials & Basic Info',
    link: '/workbench/account',
  },
  {
    id: '2',
    icon: 'SettingIcon',
    activeIcon: 'SettingIconActive',
    title: 'Notifications',
    link: '/workbench/notifications',
  },
]

const Layout: FC<layoutProps> = (props) => {
  const { children } = props
  return (
    <>
      <div className="bg-[#f7f7f7]">
        <div className="m-auto flex min-h-[calc(100vh-128px)] w-full max-w-[1920px] max-xl:min-h-[calc(100vh-122px)]">
          <AccountAside menu={asideData} />
          <div className={'flex-1 bg-[f7f7f7]'}>{children}</div>
        </div>
      </div>
    </>
  )
}

export default Layout
