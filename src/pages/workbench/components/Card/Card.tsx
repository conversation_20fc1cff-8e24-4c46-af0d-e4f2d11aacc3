import React, { FC, ReactNode } from 'react'
import classNames from 'classnames'

export interface CardPropsInterface {
  children: ReactNode
  className?: string
  title?: string | ReactNode
}

const Card: FC<CardPropsInterface> = (prop) => {
  const { children, className = '', title = '', ...resProps } = prop
  return (
    <div
      className={classNames(
        className,
        'm-[24px] rounded-[12px] bg-[#fff] p-[24px] shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)]',
      )}
      {...resProps}
    >
      {title ? (
        <h2 className="mb-[20px] text-base font-semibold text-[#19191a]">
          {title}
        </h2>
      ) : (
        ''
      )}
      <div className="content">{children}</div>
    </div>
  )
}

export default Card
