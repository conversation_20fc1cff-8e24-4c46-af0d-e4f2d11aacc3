import { useState, FC, useEffect } from 'react'
import classNames from 'classnames'
import { useRouter } from 'next/router'
import UseIcon, { IconMap } from '../Icon/Icon'

export interface AsideItemInterface {
  id: string
  icon: IconMap
  activeIcon: IconMap
  title: string
  link?: string
  children?: AsideItemInterface[]
}

export interface AsideProps {
  menu: AsideItemInterface[]
}

const AccountAside: FC<AsideProps> = (props) => {
  const router = useRouter()
  const { menu = [] } = props
  const [activeIndex, setActive] = useState('-1')

  useEffect(() => {
    if (router) {
      const activeMenu = menu.find((item) => item.link === router.pathname)
      if (activeMenu) {
        setActive(activeMenu.id || menu[0].id)
      }
      return () => {}
    }
  }, [menu, router])

  const handleMenuClick = (val: AsideItemInterface) => {
    setActive(val.id)
    if (val.link) {
      window.location.href = val.link
    }
  }

  return (
    <div className="flex w-[300px] flex-[0_0_300px] flex-col bg-[#fff] px-[24px] py-[20px]">
      {menu.map((item, index) => {
        return (
          <div
            key={index}
            className={classNames(
              'flex cursor-pointer items-center gap-x-2.5 rounded px-5 py-4 text-[14px] font-semibold leading-[22px]',
              activeIndex === item.id ? 'bg-[#f2f2f2]' : 'hover:bg-hoverBgRgba',
            )}
            onClick={() => handleMenuClick(item)}
          >
            <UseIcon
              icon={activeIndex === item.id ? item.activeIcon : item.icon}
            />
            <span className="text-[#19191a]">{item.title}</span>
          </div>
        )
      })}
    </div>
  )
}

export default AccountAside
