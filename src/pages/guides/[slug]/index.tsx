import dayjs from 'dayjs'
import clsx from 'clsx'
import { WithDocsSide, type LayoutProps } from '@/components'
import { getDocPageSlugs, getDocPageBySlug, MapMdx, IPageMeta } from '@/docs'

function renderMdx(slug: string) {
  const Component = MapMdx[slug].Component
  return <Component />
}

function Page({ data }: { data: IPageMeta }) {
  if (!data) {
    return (
      <>
        <div hidden />
        <div className="relative mx-auto">
          <h3 className="mb-5 text-[24px]/[32px] font-semibold text-textColorBlack">
            Not found
          </h3>
        </div>
      </>
    )
  }

  const { title, slug, updatedAt } = data
  const updateDate = dayjs(new Date(updatedAt)).format('MMM DD, YYYY')

  const isLight = () => ['conditions'].includes(slug)

  return (
    <>
      <div hidden />
      <div className="relative mx-auto">
        <h3 className="mb-5 text-[24px]/[32px] font-semibold text-textColorBlack">
          {title}
        </h3>
        <div className="mb-5 text-[12px]/[20px]">
          Last updated: {updateDate}
        </div>

        <div className={clsx('prose max-w-none', { 'prose-light': isLight() })}>
          {renderMdx(slug)}
        </div>
      </div>
    </>
  )
}

export default WithDocsSide<{ data: IPageMeta } & LayoutProps>(Page)

export async function getStaticPaths() {
  const slugs = await getDocPageSlugs()

  console.log(`[guides] getStaticPaths=>`, slugs.length)

  const paths = slugs.map((slug) => ({ params: { slug } }))
  return {
    paths,
    fallback: 'blocking',
  }
}

export async function getStaticProps({ params }: { params: { slug: string } }) {
  const { slug } = params
  const data = await getDocPageBySlug(slug)

  console.log(`[guides] getStaticProps=>`, data)

  const { anchors, breadcrumbs, noWrap = false } = data

  return {
    props: {
      data,
      noWrap,
      anchors: anchors.map((title) => ({ title })),
      breadcrumbs: [
        ...(breadcrumbs && breadcrumbs?.length ? breadcrumbs : []),
        {
          title: data.title,
        },
      ],
    },
  }
}
