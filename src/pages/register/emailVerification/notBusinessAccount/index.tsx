export default function BusinessAccountSuccess() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-white">
      <div className="shadow-custom flex w-[680px] flex-col items-center rounded-lg bg-white p-[32px]">
        {/* 成功提示部分 */}
        <div className="text-center">
          <div className="mb-4 flex items-center justify-center gap-2">
            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-100">
              <svg
                className="h-4 w-4 text-green-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <span className="text-[20px] font-semibold text-[#19191A]">
              We have received your business account application!
            </span>
          </div>
        </div>

        {/* 说明文字部分 */}
        <div className="font-sans text-[14px] leading-[22px] text-[#707070]">
          <p className="text-center">
            We will process your application within 1-2 business days. During
            the review process, you can use this email and password to log in to{' '}
            <a
              href="https://fs.com"
              className="text-[#0066FF] hover:text-blue-700"
            >
              FS.com
            </a>{' '}
            and{' '}
            <a href="#" className="text-[#0066FF] hover:text-blue-700">
              FS Developer Portal
            </a>
            . Meanwhile, you can check the review progress in{' '}
            <a href="#" className="text-[#0066FF] hover:text-blue-700">
              Workbench
            </a>
            . Thank you for your understanding!
          </p>
        </div>
      </div>
    </div>
  )
}
