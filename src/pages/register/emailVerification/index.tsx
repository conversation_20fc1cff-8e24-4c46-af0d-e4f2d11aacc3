import message from '@/components/ui/Message'
import userApi from '@/services/api/user'
import aesUtils from '@/utils/crypto'
import { MessageOutlined, PhoneOutlined, MailOutlined } from '@ant-design/icons'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { useState, useEffect } from 'react'

export default function EmailVerification() {
  const router = useRouter()
  const { email } = router.query
  const [isResending, setIsResending] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [decryptedEmail, setDecryptedEmail] = useState('')

  useEffect(() => {
    if (email) {
      const decryptedEmail = aesUtils.decrypt(
        email as string,
        '_-yu_xuan_3507-_',
        'fs_com_phone2016',
      )
      console.log(decryptedEmail)
      setDecryptedEmail(decryptedEmail)
    }
  }, [email])

  useEffect(() => {
    let timer: NodeJS.Timeout
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1)
      }, 1000)
    }
    return () => {
      if (timer) {
        clearInterval(timer)
      }
    }
  }, [countdown])

  interface ApiError {
    code: number
    message: string
  }
  const handleResendEmail = async () => {
    try {
      setIsResending(true)
      const res = await userApi.resendVerifyEmail({
        params: aesUtils.encrypt(
          JSON.stringify({ email: decryptedEmail, type: 1 }),
          '_-yu_xuan_3507-_',
          'fs_com_phone2016',
        ),
        isOpenPlatform: true,
      })
      if (res) {
        setCountdown(60)
      }
    } catch (error: unknown) {
      const err = error as ApiError
      message.error('Failed to resend email: ' + err.message)
    } finally {
      setIsResending(false)
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-white">
      <div className="shadow-custom flex w-[920px] flex-col items-center rounded-lg bg-white p-[64px]">
        {/* Logo和标题部分 */}
        <div className="mb-[52px] flex items-center">
          {/* <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center text-white font-bold">
            <span>FS</span>
          </div> */}
          <Image src="/logo.svg" alt="Fs logo" width={76} height={36} />
          <div className="ml-[16px] mr-[16px] h-[28px] w-[1px] bg-[#E6E6E6]"></div>
          <h1 className="text-[20px] font-semibold text-[#19191A]">
            Verify Your Email
          </h1>
        </div>

        {/* 验证信息部分 */}
        <div className="mb-[16px] text-center">
          <div className="mb-4 flex items-center justify-center gap-2">
            <div className="iconfont flex h-6 w-6 items-center justify-center text-green-500">
              &#xf060;
            </div>
            <span className="text-[20px] text-xl font-semibold text-[#19191A]">
              You&apos;re almost there!
            </span>
          </div>
          <p className="line-height-[22px] font-sans text-[#707070]">
            We have sent a verification email to{' '}
            <span className="text-blue-600">{decryptedEmail}</span>. Follow the
            instructn the email to activate your account.
          </p>
          <p className="line-height-[22px] font-sans text-sm text-[#707070]">
            If you don&apos;t see the email, check your spam/junk folder
          </p>
        </div>

        {/* 重发按钮 */}
        <button
          className="mb-[48px] rounded-md bg-[#F2F2F2] px-6 py-2.5 text-gray-600 transition-colors hover:bg-gray-200 disabled:cursor-not-allowed disabled:opacity-50"
          onClick={handleResendEmail}
          disabled={isResending || countdown > 0}
        >
          {isResending
            ? 'Resending...'
            : countdown > 0
              ? `Resend in ${countdown}s`
              : 'Resend Email'}
        </button>

        {/* 帮助信息部分 */}
        <div className="w-full">
          <h2 className="mb-[16px] text-center font-sans font-medium text-[#19191A]">
            Need help?
          </h2>
          <div className="line-height-[22px] flex justify-center gap-8 font-sans text-[13px] text-[#707070]">
            <div className="flex items-center gap-2 text-gray-600">
              <MessageOutlined className="text-[16px]" />
              <span>Live Chat</span>
            </div>
            <div className="flex items-center gap-2 text-gray-600">
              <PhoneOutlined className="text-[16px]" />
              <span>+****************</span>
            </div>
            <div className="flex items-center gap-2 text-gray-600">
              <MailOutlined className="text-[16px]" />
              <span><EMAIL></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
