import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import { userApi } from '@/services/api/user'
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons'

export default function EmailActive() {
  const router = useRouter()
  const { params } = router.query
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<{
    success: boolean
    message: string
  } | null>(null)

  useEffect(() => {
    if (!params) return

    const activateEmail = async () => {
      setIsLoading(true)
      try {
        const res = await userApi.activeEmail({ params, isOpenPlatform: true })
        if (res.data.resultType === 1 || res.data.resultType === 3) {
          router.push({
            pathname: '/register/emailVerification/notBusinessAccount',
          })
        } else if (res.data.resultType === 2) {
          router.push({
            pathname: '/register/emailVerification/hasBusinessAccount',
            query: {
              id: res.data.cid,
              from: 'active',
            },
          })
        }
      } catch (error: unknown) {
        console.log(error)
        setResult({
          success: false,
          message: 'Sorry. The link has expired',
        })
      } finally {
        setIsLoading(false)
      }
    }

    activateEmail()
  }, [params, router])

  return (
    <div className="flex h-screen items-center justify-center">
      {isLoading ? (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm">
          <div className="space-y-4">
            <div className="flex justify-center">
              <div className="h-16 w-16 animate-spin rounded-full border-4 border-gray-200 border-t-blue-500 shadow-lg" />
            </div>
          </div>
        </div>
      ) : (
        <div className="shadow-custom rounded-lg bg-white p-8 text-center">
          {result && (
            <div className="space-y-4">
              <div
                className={`text-4xl ${
                  result.success ? 'text-green-500' : 'text-red-500'
                }`}
              >
                {result.success ? (
                  <CheckCircleOutlined />
                ) : (
                  <CloseCircleOutlined />
                )}
              </div>
              <p className="text-lg text-gray-700">{result.message}</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
