import type { AppProps } from 'next/app'
import Head from 'next/head'
import { Open_Sans } from 'next/font/google'

import '@/styles/globals.css'
import { Header, Footer } from '@/components'
import { MenuProvider, IMenusItem } from '@/contexts'
import { getMenus } from '@/api'

const openSans = Open_Sans({
  variable: '--font-open-sans',
  subsets: ['latin'],
  weight: ['400', '600'],
})

function App({
  Component,
  pageProps,
  menus,
}: AppProps & { menus: IMenusItem[] }) {
  return (
    <>
      <Head>
        <title>FS Developer Portal</title>
        <meta
          name="viewport"
          content="minimum-scale=1, initial-scale=1, width=device-width, user-scalable=no"
        />
        <link rel="shortcut icon" href={'/favicon.ico'} />
      </Head>

      <MenuProvider menus={menus}>
        <div
          className={`${openSans.variable} max-w-screen relative font-[family-name:var(--font-open-sans)] text-sm text-textColorGray`}
        >
          <Header />
          <main className="relative min-h-[calc(100vh-128px)] max-xl:min-h-[calc(100vh-122px)]">
            <Component {...pageProps} />
          </main>
          <Footer />
          <h1 className="absolute z-[-1] size-0 indent-[-99999px] text-[0]">
            Fs OpenAPI
          </h1>
        </div>
      </MenuProvider>
    </>
  )
}

App.getInitialProps = async () => {
  const menus = await getMenus()
  return {
    menus,
  }
}

export default App
