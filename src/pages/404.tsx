import FsButton from '@/components/ui/FsButton'
import { useRouter } from 'next/router'
import { useEffect } from 'react'

export default function Custom404() {
  const router = useRouter()

  useEffect(() => {
    // 记录404错误
    console.error('404 - Page not found:', router.asPath)
  }, [router.asPath])

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="mb-4 text-6xl font-bold text-gray-900">404</h1>
        <h2 className="mb-6 text-2xl font-semibold text-gray-700">
          Page not found
        </h2>
        <p className="mb-8 text-gray-600">
          Sorry, the page you are looking for does not exist or has been
          removed.
        </p>
        <FsButton onClick={() => router.push('/')}>Back to Home</FsButton>
      </div>
    </div>
  )
}
