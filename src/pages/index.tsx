import React from 'react'
import { Search, Cards } from '@/components'
import { useMenu } from '@/contexts'

export default function Home() {
  const { menus: allMenus } = useMenu()

  const menus = allMenus.filter((v) => v.brief && v.icon)
  const baseUrl = process.env.NEXT_PUBLIC_CDN_URL
  const bgUrl = baseUrl ? `${baseUrl}/public/bg.jpeg` : 'bg.jpeg'
  return (
    <div
      className={`bg-[size:100%_420px] bg-no-repeat px-6`}
      style={{ backgroundImage: `url('${bgUrl}')` }}
    >
      <div className="mx-auto max-w-[1200px] pb-10 pt-20">
        <div className="mb-[100px] flex flex-col items-center text-textColorBlack">
          <h2 className="mb-6 text-center text-[32px]/[40px] font-semibold">
            OpenAPI
          </h2>
          <div className="mb-6 text-center text-[16px]/[24px]">
            Get Started with FS API
          </div>
          <div className="w-[80%] max-w-[560px]">
            <Search />
          </div>
        </div>
        <div className="mx-auto max-w-[896px]">
          <Cards datas={menus} />
        </div>
      </div>
    </div>
  )
}
