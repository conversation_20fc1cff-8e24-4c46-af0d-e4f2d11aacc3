import dayjs from 'dayjs'
import {
  WithDocsSide,
  DocsTable,
  DocsResJson,
  type LayoutProps,
  DocsReverseTable,
} from '@/components'
import {
  getDocSlugs,
  getDocsByIdWithRetry,
  IDocsResData,
  ServiceType,
} from '@/api'
import { transformResponseToData, padTitleToId, assignSourceId } from '@/utils'

const Loading = () => {
  return (
    <div className="inset-x-0 top-[60px] flex items-center justify-center">
      <svg className="size-10 animate-spin" viewBox="25 25 50 50">
        <circle
          className={`animate-[dash_1.5s_ease-in-out_infinite] stroke-[#19191a]`}
          cx="50"
          cy="50"
          r="20"
          fill="none"
          strokeWidth="3"
          strokeMiterlimit="10"
          strokeDasharray="1,200"
          strokeDashoffset="0"
          strokeLinecap="round"
        />
      </svg>
    </div>
  )
}

function Page({
  data,
  isFallback,
}: {
  data: IDocsResData
  isFallback: boolean
}) {
  if (isFallback) {
    return (
      <>
        <Loading />
      </>
    )
  }

  if (!data) {
    return (
      <>
        <div hidden />
        <div className="relative mx-auto">
          <h3 className="mb-5 text-[24px]/[32px] font-semibold text-textColorBlack">
            API not found
          </h3>
        </div>
      </>
    )
  }

  const { updatedAt, document } = data || {}
  const updateDate = dayjs(new Date(updatedAt)).format('MMM DD, YYYY')

  const pathSource = {
    columns: [
      { title: 'title', dataIndex: 'title', key: 'title' },
      { title: 'value', dataIndex: 'value', key: 'value' },
    ],
    dataSource: [
      {
        title: document?.httpMethod?.toUpperCase(),
        value: document?.path,
      },
      {
        title: 'Request Data Type',
        value: document?.contentType,
      },
      // {
      //   title: 'Response Data Type',
      //   value: '[*/*]',
      // },
    ],
  }

  const paramsSource = {
    columns: [
      {
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
        width: '280px',
      },
      {
        title: 'Description',
        dataIndex: 'desc',
        key: 'description',
        render: (text: string) => {
          return <div dangerouslySetInnerHTML={{ __html: text }}></div>
        },
      },
      {
        title: 'Require',
        dataIndex: 'required',
        key: 'require',
        width: '122px',
        render: (value: boolean) =>
          value ? <span className="text-[#C00000]">true</span> : 'false',
      },
      {
        title: 'Type',
        dataIndex: 'type',
        key: 'type',
        width: '115px',
      },
    ],
    dataSource: assignSourceId(document?.parameters || []),
  }

  const statusSource = {
    columns: [
      {
        title: 'Code',
        dataIndex: 'code',
        key: 'code',
      },
      {
        title: 'Description',
        dataIndex: 'desc',
        key: 'description',
      },
    ],
    dataSource: assignSourceId(document?.responseStatus || []),
  }

  const mapRes = (document?.responses || []).reduce(
    (t, c) => ({ ...t, [c.name]: c }),
    {} as Record<string, any>,
  )
  const resSorts = ['code', 'msg', 'data']
    .map((v) => mapRes?.[v])
    .filter(Boolean)
  const responseSource = {
    columns: [
      {
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
        width: '280px',
      },
      {
        title: 'Description',
        dataIndex: 'desc',
        key: 'description',
        render: (text: string) => {
          return <div dangerouslySetInnerHTML={{ __html: text }}></div>
        },
      },
      {
        title: 'Type',
        dataIndex: 'type',
        key: 'type',
        width: '115px',
      },
    ],
    dataSource: assignSourceId(resSorts),
  }

  const responseExample =
    document?.mockResponse || transformResponseToData(responseSource.dataSource)

  const status = statusSource.dataSource.find((v) => v.code !== '200')
  const requestFailureErrors = {
    code: status?.code || '400',
    msg: status?.desc || 'Bad Request',
  }

  return (
    <>
      <div hidden />
      <div className="relative mx-auto">
        <h3 className="mb-5 text-[24px]/[32px] font-semibold text-textColorBlack">
          {document.summary}
        </h3>
        <div className="mb-5 text-[12px]/[20px]">
          Last updated: {updateDate}
        </div>

        <div
          id={padTitleToId('Resource URL')}
          className="mb-5 mt-3 text-[20px]/[28px] font-semibold text-textColorBlack"
        >
          Resource URL
        </div>
        <DocsReverseTable
          columns={pathSource.columns}
          dataSource={pathSource.dataSource}
        />

        <div
          id={padTitleToId('Interface Description')}
          className="mb-5 mt-5 text-[20px]/[28px] font-semibold text-textColorBlack"
        >
          Interface Description
        </div>
        <div className="text-[12px]/[20px]">{document.desc}</div>

        <hr className="my-6" />

        <div
          id={padTitleToId('Response Example')}
          className="mb-5 text-[20px]/[28px] font-semibold text-textColorBlack"
        >
          Response Example
        </div>
        <DocsResJson data={responseExample} />
        <div className="mt-4 text-[12px]/[20px]">
          Note: Please make sure your request URL is all in lower case and
          cannot contain any blank spaces or line breaks.
        </div>

        <hr className="my-6" />

        <div
          id={padTitleToId('Resource Information')}
          className="mb-5 text-[20px]/[28px] font-semibold text-textColorBlack"
        >
          Resource Information
        </div>
        <DocsTable
          columns={paramsSource.columns}
          dataSource={paramsSource.dataSource}
          border={true}
        />

        <hr className="my-6" />

        <div
          id={padTitleToId('Response Body')}
          className="mb-5 text-[20px]/[28px] font-semibold text-textColorBlack"
        >
          Response Body
        </div>
        <DocsTable
          columns={responseSource.columns}
          dataSource={responseSource.dataSource}
        />

        <hr className="my-6" />

        <div
          id={padTitleToId('Status')}
          className="mb-5 text-[20px]/[28px] font-semibold text-textColorBlack"
        >
          Status
        </div>
        <DocsTable
          columns={statusSource.columns}
          dataSource={statusSource.dataSource}
          border={true}
        />

        <hr className="my-6" />

        <div
          id={padTitleToId('Request Failure Errors')}
          className="mb-5 mt-3 text-[20px]/[28px] font-semibold text-textColorBlack"
        >
          Request Failure Errors
        </div>
        <DocsResJson data={requestFailureErrors} />
      </div>
    </>
  )
}

export default WithDocsSide<
  { data: IDocsResData } & { isFallback: boolean } & LayoutProps
>(Page)

export async function getStaticPaths() {
  const list = await getDocSlugs()
  console.log(`[docs] getStaticPaths=>`, list.length)

  const path = list.map((slug) => ({
    params: {
      slug,
    },
  }))

  return {
    paths: [...path, { params: { slug: '7' } }],
    fallback: true,
  }
}

export async function getStaticProps({ params }: { params: { slug: string } }) {
  const data = await getDocsByIdWithRetry(params.slug)
    .then((res) => res)
    .catch(() => null)
  const { id, description, serviceType, document } = data || {}
  console.log(`[docs] getStaticProps=>`, {
    id,
    description,
    summary: document?.summary,
  })

  const anchors = [
    {
      title: 'Resource URL',
    },
    {
      title: 'Interface Description',
    },
    {
      title: 'Response Example',
    },
    {
      title: 'Resource Information',
    },
    {
      title: 'Response Body',
    },
    {
      title: 'Status',
    },
    {
      title: 'Request Failure Errors',
    },
  ]

  const breadcrumbs = [
    {
      title:
        serviceType === ServiceType.Order
          ? 'Order Management'
          : 'Item Management',
      url: `/docs?serviceType=${serviceType}`,
    },
    ...(document?.summary
      ? [
          {
            title: document?.summary,
          },
        ]
      : []),
  ]

  return {
    props: {
      data,
      anchors,
      breadcrumbs,
    },
    revalidate: 3600,
  }
}
