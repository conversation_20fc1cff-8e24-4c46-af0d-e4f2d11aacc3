import dayjs from 'dayjs'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import {
  WithDocsSide,
  DocsTable,
  type LayoutProps,
  Breadcrumb,
} from '@/components'
import { getDocs, IDocsResData, IDocsGroup, ServiceType } from '@/api'

function Page({ groups }: { groups: IDocsGroup }) {
  const searchParams = useSearchParams()

  const serviceType = (searchParams?.get('serviceType') ||
    ServiceType.Order) as ServiceType

  const title =
    serviceType == ServiceType.Order ? 'Order Management' : 'Item Management'
  const list = groups[serviceType]

  const date = (list || []).sort((a, b) =>
    a.createdAt < b.createdAt ? 1 : -1,
  )[0]?.createdAt
  const updateDate = dayjs(new Date(date)).format('MMM DD, YYYY')

  const source = {
    columns: [
      {
        title: 'Function',
        dataIndex: 'summary',
        key: 'summary',
        render: (text: string, record: IDocsResData) => {
          return (
            <Link href={`/docs/${record.id}`} className="text-textColorLink">
              {text}
            </Link>
          )
        },
      },
      {
        title: 'Description',
        dataIndex: 'desc',
        key: 'desc',
      },
    ],
    dataSource: (list || []).map((v) => ({
      id: v.id,
      summary: v?.document?.summary,
      desc: v?.document?.desc,
    })),
  }

  const breadcrumbs = [
    {
      title,
    },
  ]

  return (
    <>
      <Breadcrumb className="mb-3" datas={breadcrumbs} />

      <div className="relative mx-auto">
        <h3 className="mb-5 text-[24px]/[32px] font-semibold text-textColorBlack">
          {title}
        </h3>
        <div className="mb-5 text-[12px]/[20px]">
          Last updated: {updateDate}
        </div>

        <DocsTable columns={source.columns} dataSource={source.dataSource} />
      </div>
    </>
  )
}

export default WithDocsSide<{ groups: IDocsGroup } & LayoutProps>(Page)

export async function getStaticProps() {
  const groups = await getDocs()

  return {
    props: {
      groups,
    },
    revalidate: 3600,
  }
}
