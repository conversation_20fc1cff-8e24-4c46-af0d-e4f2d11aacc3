import { useEffect, useState, useRef } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Input } from '../ui/Input'
import { CountryItem, CountrySelect, State } from '../ui/CountrySelect'
import { PhoneNumber } from '../ui/PhoneNumber'
import apiService from '@/services/api/country'
import { Select } from '../ui/Select'
import { PwdVerify, PwdVerifyRef } from '../ui/PwdVerify'
import FsButton from '../ui/FsButton'
import userApi from '@/services/api/user'
import aesUtils from '@/utils/crypto'
import { useRouter } from 'next/router'
import message from '../ui/Message'
// import { debounce } from 'lodash'

// 邮箱静态校验
const validateEmail = async (
  email: string,
  triggerType: 'blur' | 'change' | 'submit',
) => {
  if (!email.replace(/\s/g, '')) {
    return 'Please enter the email'
  }
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.replace(/\s/g, ''))) {
    return 'Please enter a valid email address'
  }
  if (triggerType === 'change') return ''
  try {
    const data = await emailVerify(email)
    if (data.is_registered === 1) {
      return 'This email is already registered'
    }
    if (data.is_company_email === 0) {
      return 'Please use a corporate email domain.'
    }
  } catch (error) {
    console.error('Email verification failed:', error)
    return 'Failed to verify email'
  }
  return ''
}

const validateField = (
  name: string,
  value: string | boolean | number,
  triggerType: 'blur' | 'change' | 'submit',
) => {
  switch (name) {
    case 'organizationName': {
      if (!value) return 'Please enter the organization name'
      const trimmedValue = String(value).replace(/\s/g, '')
      if (trimmedValue.length < 2)
        return 'Organization name must be at least 2 characters'
      if (trimmedValue.length > 120)
        return 'Organization name cannot exceed 120 characters'
      return ''
    }
    case 'country':
      return !value ? 'Please select the country/region' : ''
    case 'state':
      return !value ? 'Please select the state/province' : ''
    case 'email':
      return typeof value === 'string' ? validateEmail(value, triggerType) : ''
    case 'phoneNumber':
      return !value ? 'Please enter the phone number' : ''
    case 'firstName': {
      if (!value) return 'Please enter the first name'
      const trimmedValue = String(value).replace(/\s/g, '')
      if (trimmedValue.length < 2)
        return 'First name must be at least 2 characters'
      if (trimmedValue.length > 40)
        return 'First name cannot exceed 40 characters'
      return ''
    }
    case 'lastName': {
      if (!value) return 'Please enter the last name'
      const trimmedValue = String(value).replace(/\s/g, '')
      if (trimmedValue.length < 2)
        return 'Last name must be at least 2 characters'
      if (trimmedValue.length > 40)
        return 'Last name cannot exceed 40 characters'
      return ''
    }
    // case 'employeeSize':
    //   return !value ? 'Please enter the employee size' : ''
    case 'streetAddress1': {
      if (!value) return 'Please enter the street address'
      const trimmedValue = String(value).replace(/\s/g, '')
      if (trimmedValue.length < 2)
        return 'Street address must be at least 2 characters'
      if (trimmedValue.length > 35)
        return 'Street address cannot exceed 35 characters'
      return ''
    }
    case 'city': {
      if (!value) return 'Please enter the city'
      const trimmedValue = String(value).replace(/\s/g, '')
      if (trimmedValue.length < 2) return 'City must be at least 2 characters'
      if (trimmedValue.length > 40) return 'City cannot exceed 40 characters'
      return ''
    }
    case 'agreeToPrivacy':
      return !value
        ? 'Please make sure you agree to our Privacy Policy and Terms of Use.'
        : ''
    case 'agreeToTerms':
      return !value
        ? 'Please make sure you agree to our Privacy Policy and Terms of Use.'
        : ''
    default:
      return ''
  }
}

const emailVerify = async (email: string) => {
  const res = await userApi.verifyEmail({
    email_address: email,
    customer_is_business_type: 1,
  })
  return res.data
}
const obj = {
  organizationName: '',
  country: 0,
  streetAddress1: '',
  streetAddress2: '',
  city: '',
  state: '',
  zipCode: '',
  areaCode: '+1',
  phoneNumber: '',
  vat: '',
  employeeSize: '',
  firstName: '',
  lastName: '',
  email: '',
  password: '',
  agreeToPrivacy: true,
  agreeToTerms: true,
}

interface ApiError {
  code: number
  message: string
  errors?: Record<string, string>
}

export function OrganizationRegister() {
  const [formData, setFormData] = useState(obj)

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})

  const pwdVerifyRef = useRef<PwdVerifyRef>(null)

  const validateFormField = async (
    name: string,
    value: string | boolean | number,
    triggerType: 'blur' | 'change' | 'submit',
  ) => {
    // 首先进行基础校验
    const basicError = validateField(name, value, triggerType)
    if (basicError) return basicError
    if (name === 'password') {
      const pwdValidation = pwdVerifyRef.current?.validateWithRules()
      if (pwdValidation && !pwdValidation.isValid) {
        return 'Please enter a valid password'
      }
    }

    return ''
  }

  const handleBlur = (name: string) => async () => {
    setTouched((prev) => ({ ...prev, [name]: true }))
    const error = await validateFormField(
      name,
      formData[name as keyof typeof formData],
      'blur',
    )
    setErrors((prev) => ({ ...prev, [name]: error }))
  }

  const validateForm = async () => {
    const newErrors: Record<string, string> = {}
    const validationPromises = Object.keys(formData).map(async (key) => {
      const error = await validateFormField(
        key,
        formData[key as keyof typeof formData],
        'submit',
      )
      if (error) {
        newErrors[key] = error
      }
    })

    await Promise.all(validationPromises)
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    const isValid = await validateForm()
    console.log(isValid)
    if (isValid) {
      setIsLoading(true)
      console.log('企业注册表单数据:', formData)
      const params = {
        source: 35,
        customer_is_business_type: 1,
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        password: formData.password,
        company_name: formData.organizationName,
        entry_country_id: formData.country,
        address_one: formData.streetAddress1,
        address_two: formData.streetAddress2,
        city: formData.city,
        entry_postcode: formData.zipCode,
        company_phone: formData.phoneNumber,
        tax_number: formData.vat,
        organization_size: formData.employeeSize || undefined,
        state: formData.state,
      }
      try {
        const res = await userApi.register(params)
        console.log(res)
        setIsLoading(false)
        const encryptedEmail = aesUtils.encrypt(
          formData.email,
          '_-yu_xuan_3507-_',
          'fs_com_phone2016',
        )
        router.push({
          pathname: '/register/emailVerification',
          query: {
            email: encryptedEmail,
          },
        })
        console.log(encryptedEmail)
      } catch (error) {
        console.log(error)
        setIsLoading(false)
        const err = error as ApiError
        if (err.code === 422) {
          if (err.errors) {
            let errorStr = ''
            for (const key in err.errors) {
              errorStr += `${key}: ${err.errors[key]}\n`
            }
            message.error(errorStr)
          }
        }

        if (err.code === 403 || err.code === 400) {
          message.error(err.message)
        }
      }
    }
  }

  const handleChange =
    (name: string) => async (value: string | boolean | number) => {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }))
      if (touched[name]) {
        const error = await validateFormField(name, value, 'change')
        setErrors((prev) => ({ ...prev, [name]: error }))
      }
      if (name === 'agreeToPrivacy' || name === 'agreeToTerms') {
        const error = await validateFormField(name, value, 'change')
        setErrors((prev) => ({ ...prev, [name]: error }))
      }
    }
  const [countryList, setCountryList] = useState<CountryItem[]>([])
  const fetchCountries = async () => {
    const res = await apiService.getCountryList()
    console.log(res.data.countryList.source)
    setCountryList(res.data.countryList.source)
    setFormData((prev) => ({
      ...prev,
      country: res.data.countryList.source[0].countries_id,
      areaCode: res.data.tel_prefix,
    }))
    handleStateListChange(res.data.countryList.source[0].states)
  }

  const formatAreaCodeOptions = (countries: CountryItem[]) => {
    return countries.map((country) => ({
      value: country.tel_prefix,
      label: `${country.countries_name} (${country.tel_prefix})`,
      searchLabel: `${country.countries_name} ${country.countries_chinese_name} ${country.tel_prefix}`,
    }))
  }

  const [stateList, setStateList] = useState<
    { value: string; label: string }[]
  >([])

  const handleStateListChange = (states: State[]) => {
    console.log(states)
    if (states.length > 0) {
      setStateList(
        states.map((state) => ({
          value: state.states_code,
          label: state.states,
        })),
      )
      setFormData((prev) => ({
        ...prev,
        state: states[0].states_code,
      }))
    }
  }

  useEffect(() => {
    fetchCountries()
  }, [])

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-[#ffffff]">
      <div className="mb-[20px] mt-[60px] flex w-[1000px] justify-center gap-2 rounded-[4px] bg-[#0060BF] bg-opacity-[0.04] px-4 py-3">
        <span className="iconfont h-[16px] w-[16px] text-[#0060BF]">
          &#xe718;
        </span>
        <div className="text-[14px] leading-[22px] text-[#707070]">
          FS Developer Portal APIs is currently available exclusively to FS
          Business Account customers. To explore and experience our API
          services, please simply complete your FS Business Account verification
          first!
        </div>
      </div>
      <div className="mb-[60px] flex w-[1000px] justify-center rounded-[8px] bg-white py-16">
        <div className="w-[400px]">
          <div className="mb-8 flex items-center">
            <Link href="/">
              <Image
                src="https://resource.fs.com/mall/generalImg/20230113114219j179i6.svg"
                alt="FS logo"
                width={75}
                height={36}
                className="mr-5"
              />
            </Link>
            <h1 className="text-xl font-semibold text-[#19191A]">
              Create Business Account
            </h1>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <h2 className="mb-4 text-[14px] font-semibold text-[#19191A]">
                Organization Information
              </h2>

              <Input
                label="Organization Name"
                name="organizationName"
                value={formData.organizationName}
                onChange={(e) =>
                  handleChange('organizationName')(e.target.value)
                }
                onBlur={handleBlur('organizationName')}
                required
                error={errors.organizationName}
              />

              <CountrySelect
                label="Country/Region"
                options={countryList}
                value={formData.country}
                onChange={(value) => handleChange('country')(value)}
                onBlur={handleBlur('country')}
                onStateListChange={handleStateListChange}
                required
                error={errors.country}
                placeholder="Select Country/Region"
              />

              <Input
                label="Street Address"
                name="streetAddress1"
                value={formData.streetAddress1}
                onChange={(e) => handleChange('streetAddress1')(e.target.value)}
                onBlur={handleBlur('streetAddress1')}
                required
                error={errors.streetAddress1}
              />

              <Input
                label="Street Address 2"
                name="streetAddress2"
                value={formData.streetAddress2}
                onChange={(e) => handleChange('streetAddress2')(e.target.value)}
                onBlur={handleBlur('streetAddress2')}
                error={errors.streetAddress2}
              />

              <Input
                label="City"
                name="city"
                value={formData.city}
                onChange={(e) => handleChange('city')(e.target.value)}
                onBlur={handleBlur('city')}
                required
                error={errors.city}
              />

              <Select
                label="State/Province"
                options={stateList}
                value={formData.state}
                onChange={(value) => handleChange('state')(value)}
                required
                error={errors.state}
              />

              <Input
                label="Zip Code"
                name="zipCode"
                value={formData.zipCode}
                onChange={(e) => handleChange('zipCode')(e.target.value)}
                onBlur={handleBlur('zipCode')}
                error={errors.zipCode}
              />

              <div className="">
                <PhoneNumber
                  label="Phone Number"
                  areaCode={formData.areaCode}
                  phoneNumber={formData.phoneNumber}
                  areaCodeOptions={formatAreaCodeOptions(countryList)}
                  onAreaCodeChange={(value) => handleChange('areaCode')(value)}
                  onPhoneNumberChange={(value) =>
                    handleChange('phoneNumber')(value)
                  }
                  onBlur={handleBlur('phoneNumber')}
                  required
                  error={errors.phoneNumber}
                />
              </div>

              <Input
                label="VAT"
                name="vat"
                value={formData.vat}
                onChange={(e) => handleChange('vat')(e.target.value)}
                onBlur={handleBlur('vat')}
                error={errors.vat}
              />

              {/* <Select
                label="Employee Size"
                showSearch={false}
                options={[
                  { value: '2', label: 'SMB' },
                  { value: '3', label: 'Large Enterprises' },
                  { value: '4', label: 'Global Accounts' },
                ]}
                value={formData.employeeSize}
                onChange={(value) => handleChange('employeeSize')(value)}
                required
                error={errors.employeeSize}
              /> */}
            </div>

            <div className="">
              <h2 className="mb-4 text-[14px] font-semibold text-[#19191A]">
                Account information
              </h2>

              <div className="flex gap-[12px]">
                <div className="w-1/2">
                  <Input
                    label="First name"
                    name="firstName"
                    value={formData.firstName}
                    onChange={(e) => handleChange('firstName')(e.target.value)}
                    onBlur={handleBlur('firstName')}
                    className=""
                    required
                    error={errors.firstName}
                  />
                </div>
                <div className="w-1/2">
                  <Input
                    label="Last name"
                    name="lastName"
                    value={formData.lastName}
                    onChange={(e) => handleChange('lastName')(e.target.value)}
                    onBlur={handleBlur('lastName')}
                    className=""
                    required
                    error={errors.lastName}
                  />
                </div>
              </div>

              <Input
                label="Email"
                name="email"
                value={formData.email}
                onChange={(e) => handleChange('email')(e.target.value)}
                onBlur={handleBlur('email')}
                required
                error={errors.email}
              />

              <PwdVerify
                ref={pwdVerifyRef}
                label="Password"
                value={formData.password}
                onChange={(value) => handleChange('password')(value)}
                onBlur={handleBlur('password')}
                required
                error={errors.password}
              />

              <div className="mt-6 flex items-center"></div>
            </div>

            <div className="mb-6">
              <label className="block">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="agreeToPrivacy"
                    checked={formData.agreeToPrivacy}
                    onChange={(e) =>
                      handleChange('agreeToPrivacy')(e.target.checked)
                    }
                    className="custom-checkbox mr-2 h-[16px] w-[16px] text-[16px]"
                    required
                  />
                  <span className="text-[12px] text-[#707070]">
                    I agree to FS&apos;s{' '}
                    <a
                      href="https://www.fs.com/policies/privacy_policy.html"
                      target="_blank"
                      className="text-[#0060BF] hover:underline"
                    >
                      Privacy Policy
                    </a>{' '}
                    and{' '}
                    <a
                      href="https://www.fs.com/policies/terms_of_use.html"
                      target="_blank"
                      className="text-[#0060BF] hover:underline"
                    >
                      Term of Use
                    </a>
                  </span>
                </div>
                {errors.agreeToPrivacy && (
                  <span className="mt-[4px] text-[12px] font-normal text-red-500">
                    {errors.agreeToPrivacy}
                  </span>
                )}
              </label>

              <label className="mt-[16px] block">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="agreeToTerms"
                    checked={formData.agreeToTerms}
                    onChange={(e) =>
                      handleChange('agreeToTerms')(e.target.checked)
                    }
                    className="custom-checkbox mr-2 h-[16px] w-[16px] text-[16px]"
                    required
                  />
                  <span className="text-[12px] text-[#707070]">
                    I agree to {''}
                    <a
                      href="https://www.fs.com/policies/business_accounts_terms_conditions"
                      target="_blank"
                      className="text-[#0060BF] hover:underline"
                    >
                      FS Business Accounts Terms & Conditions
                    </a>
                    .
                  </span>
                </div>
                {errors.agreeToTerms && (
                  <span className="mt-[4px] text-[12px] font-normal text-red-500">
                    {errors.agreeToTerms}
                  </span>
                )}
              </label>
            </div>

            <FsButton
              type="red"
              htmlType="submit"
              className="w-full"
              loading={isLoading}
            >
              Create Business Account
            </FsButton>
          </form>

          <div className="mt-6 text-center text-sm text-[#707070]">
            Already have an account?{' '}
            <a
              href="https://www.fs.com/login.html"
              target="_blank"
              className="text-[#0060BF]"
            >
              Sign in
            </a>
          </div>
        </div>

        <div className="mx-[60px] w-[1px] bg-[#E5E5E5]" />

        <div className="flex w-[400px] flex-col">
          <h2 className="mb-[48px] text-xl font-semibold text-[#19191A]">
            Benefits of FS Business Account
          </h2>

          <div className="space-y-8">
            <div className="flex items-start gap-[12px]">
              {/* <Image
                src="https://resource.fs.com/mall/generalImg/20250305114308d9dihh.svg"
                alt="Track"
                width={42}
                height={42}
              /> */}
              <div>
                <h3 className="mb-2 text-[13px] font-semibold leading-[20px] text-[#19191A]">
                  Quickly Track Enterprise-level Data
                </h3>
                <p className="text-[12px] leading-[20px] text-[#707070]">
                  Easily track and manage orders, quotes, returns, invoices, and
                  purchase history in a centralized dashboard for better
                  decision-making.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-[12px]">
              {/* <Image
                src="https://resource.fs.com/mall/generalImg/20250305114308pyhgys.svg"
                alt="Collaborate"
                width={42}
                height={42}
              /> */}
              <div>
                <h3 className="mb-2 text-[13px] font-semibold leading-[20px] text-[#19191A]">
                  Easily Collaborate with Your Colleague
                </h3>
                <p className="text-[12px] leading-[20px] text-[#707070]">
                  Efficiently manage your team by adding multiple members with
                  roles and permissions, and streamline purchasing workflows
                  with secure and structured access.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-[12px]">
              {/* <Image
                src="https://resource.fs.com/mall/generalImg/202503051143089e8kdp.svg"
                alt="Security"
                width={42}
                height={42}
              /> */}
              <div>
                <h3 className="mb-2 text-[13px] font-semibold leading-[20px] text-[#19191A]">
                  Business Level Account Confidentiality
                </h3>
                <p className="text-[12px] leading-[20px] text-[#707070]">
                  Enterprise-level certification, self-managed tax exemption and
                  address management for easier and safer sharing.
                </p>
              </div>
            </div>
            <div className="flex items-start gap-[12px]">
              <div>
                <h3 className="mb-2 text-[13px] font-semibold leading-[20px] text-[#19191A]">
                  API Access for Seamless Integration
                </h3>
                <p className="text-[12px] leading-[20px] text-[#707070]">
                  Apply to integrate fs.com with your internal systems using API
                  to check item and order info, track shipments, and manage
                  business data effortlessly.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
