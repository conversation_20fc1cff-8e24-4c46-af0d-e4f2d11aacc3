import Link from 'next/link'

export function Footer() {
  const datas = [
    {
      title: 'Site Map',
      url: 'https://www.fs.com/site_map.html',
    },
    {
      title: 'Terms and Conditions',
      url: '/guides/conditions',
    },
  ]
  return (
    <footer className="border-t border-solid border-[#E5E5E5] px-6 py-6 text-xs text-textColorGray">
      <div className="mx-auto flex max-w-[1200px] flex-col items-center justify-between gap-1 lg:flex-row">
        <div>
          Copyright © {new Date().getFullYear()} FS Developer Portal. All
          Rights Reserved.
        </div>
        <div className="relative">
          {datas.map((data, index) => {
            return (
              <span key={index}>
                <Link href={data.url}>{data.title}</Link>
                {index < datas.length - 1 && (
                  <span className="mx-5 text-xs text-[#E5E5E5] max-md:mx-2">
                    |
                  </span>
                )}
              </span>
            )
          })}
        </div>
      </div>
    </footer>
  )
}

export default Footer
