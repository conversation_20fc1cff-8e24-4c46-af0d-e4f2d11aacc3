import { FC, ReactNode } from 'react'
import Image from 'next/image'
import classNames from 'classnames'

interface TipsProps {
  children: ReactNode
  className?: string
}

const Tips: FC<TipsProps> = (props) => {
  const { children, className, ...resetProps } = props
  return (
    <div
      className={classNames(
        'flex items-start gap-x-[8px] rounded-sm bg-[#0060BF] bg-opacity-5 px-[16px] py-[10px] text-[#707070]',
        className,
      )}
      {...resetProps}
    >
      <Image
        src="https://resource.fs.com/mall/generalImg/202503121947539es7nu.svg"
        width={16}
        height={16}
        alt=""
      />
      {children}
    </div>
  )
}

export default Tips
