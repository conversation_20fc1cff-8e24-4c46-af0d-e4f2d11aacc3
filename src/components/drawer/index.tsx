import React from 'react'
import clsx from 'clsx'
import Image from 'next/image'

const IconClose = (props: React.ComponentProps<'svg'>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="#19191A"
        d="m12 12.727l-3.592 3.592q-.16.16-.354.15T7.7 16.3t-.16-.364q0-.203.16-.363L11.273 12L7.681 8.433q-.16-.16-.15-.364t.169-.363t.364-.16q.203 0 .363.16L12 11.298l3.567-3.592q.16-.16.354-.16t.354.16q.166.165.166.366t-.166.36L12.702 12l3.592 3.592q.16.16.16.354t-.16.354q-.165.166-.366.166t-.36-.166z"
      ></path>
    </svg>
  )
}

function DrawerHeader({ onClose }: { onClose: () => void }) {
  return (
    <div className="relative border-b border-solid border-[#E5E5E5] px-4">
      <div className="flex h-12 items-center justify-between">
        <Image src="/logo.svg" alt="Fs logo" width={58} height={28} />
        <IconClose className="cursor-pointer" onClick={onClose} />
      </div>
    </div>
  )
}

export interface DrawerProps {
  /**
   * 是否可见
   * @default false
   */
  visible?: boolean
  /**
   * 是否展示遮罩
   * @default true
   */
  mask?: boolean
  /**
   * 是否允许点击遮罩关闭
   * @default true
   */
  maskClosable?: boolean

  /**
   * 抽屉的方向
   * @default 'left'
   */
  placement?: 'top' | 'right' | 'bottom' | 'left'
  /**
   * 抽屉的宽度/高度
   */
  size?: string | number
  /**
   * 抽屉的z-index
   * @default 1000
   */
  zIndex?: number

  /**
   * 关闭
   * @returns
   */
  onClose?: () => void
}

const defaultProps: DrawerProps = {
  visible: false,
  mask: true,
  maskClosable: true,
  placement: 'left',
  size: '70%',
  zIndex: 1000,
}

export function Drawer({
  children,
  ...props
}: React.PropsWithChildren<DrawerProps>) {
  const { visible, mask, maskClosable, placement, size, zIndex, onClose } = {
    ...defaultProps,
    ...props,
  }

  const handleMaskClick = () => {
    if (maskClosable && onClose) {
      onClose()
    }
  }

  return (
    <div
      className={clsx('fixed inset-0 transition-transform', {
        'pointer-events-none': !visible,
      })}
      style={{ zIndex }}
    >
      {mask && visible && (
        <div
          className="absolute inset-0 bg-black opacity-50"
          onClick={handleMaskClick}
        />
      )}
      <div
        className={clsx(
          'absolute max-w-[520px] bg-white transition-transform',
          {
            'translate-x-full transform': placement === 'right' && !visible,
            '-translate-x-full transform': placement === 'left' && !visible,
            'translate-y-full transform': placement === 'bottom' && !visible,
            '-translate-y-full transform': placement === 'top' && !visible,
          },
        )}
        style={{
          width: placement === 'left' || placement === 'right' ? size : '100%',
          height: placement === 'top' || placement === 'bottom' ? size : '100%',
        }}
      >
        <div className="relative">
          <DrawerHeader onClose={handleMaskClick} />
          <div className="relative h-[calc(100vh-48px)] overflow-y-auto p-4">
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Drawer
