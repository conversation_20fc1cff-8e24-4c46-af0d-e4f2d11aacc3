import { FC, ReactNode } from 'react'
import { CloseOutlined } from '@ant-design/icons'
import classNames from 'classnames'

interface ModalPropsInterface {
  show: boolean
  children: ReactNode
  onClose?: () => void
  title?: string | ReactNode
  footer?: ReactNode
  className?: string
  isShowFooter?: boolean
  showCloseIcon?: boolean //是否展示顶部关闭图标
}

const Modal: FC<ModalPropsInterface> = (props) => {
  const {
    show = false,
    children,
    onClose,
    title = '',
    footer = '',
    className = '',
    isShowFooter = false,
    showCloseIcon = true,
  } = props
  const handleClose = () => {
    if (onClose) {
      onClose()
    }
  }

  if (!show) {
    return null
  }

  const renderFooter = (footer: ReactNode) => {
    if (!isShowFooter) return null
    if (footer) {
      return footer
    } else {
      return (
        <div className="footer mt-4 pt-4">
          <button
            className="ml-auto flex h-[40px] cursor-pointer items-center rounded-lg border border-[rgba(0,0,0,0.15)] px-4"
            onClick={handleClose}
          >
            close
          </button>
        </div>
      )
    }
  }

  const classComputed = classNames(
    className,
    'fixed left-[50%] top-[50%] z-[110] flex max-h-[94%] w-[735px] translate-x-[-50%] translate-y-[-50%] flex-col overflow-hidden rounded-lg bg-[#fff]',
  )

  return (
    <div className="fixed inset-0 z-[100]">
      <div className={classComputed}>
        <h2 className="mb-[13px] flex text-lg font-semibold leading-5">
          <span className="flex-1">{title}</span>
          {showCloseIcon && (
            <CloseOutlined
              onClick={handleClose}
              className="cursor-pointer font-normal text-[#707070]"
            />
          )}
        </h2>
        <div>{children}</div>
        {renderFooter(footer)}
      </div>
      <div className="mask absolute inset-0 bg-[rgba(0,0,0,0.3)]"></div>
    </div>
  )
}

export default Modal
