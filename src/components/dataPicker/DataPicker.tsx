import { FC, useEffect, useState } from 'react'
import RenderDate from './Date'

export interface DatePickerProps {
  label?: string
  startTime: string
  endTime: string
  placeholder: string
  onChange: (val: { endTime: string; startTime: string }) => void
}

interface RenderContentProps {
  active: boolean //是否展示
  onChange: (key: 'startTime' | 'endTime', val: string) => void
  startTime?: string
  endTime?: string
}

interface IconProps {
  className: string
  onClick: () => void
}

// 关闭图标
const CloseIconSvg: FC<IconProps> = (props) => {
  return (
    <>
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        version="1.1"
        width="16"
        height="16"
        viewBox="0 0 16 16"
      >
        <g>
          <g transform="matrix(0.7071067690849304,0.7071067690849304,-0.7071067690849304,0.7071067690849304,1.7815201652138057,-1.3266658677621308)">
            <path
              d="M2.9921895,2.487150099609375L2.4921875,2.487152099609375L2.4921875,1.487152099609375L2.9921895,1.487154364579375L18.9921875,1.487154364579375L19.4921875,1.487152099609375L19.4921875,2.487152099609375L18.9921875,2.487150099609375L2.9921895,2.487150099609375Z"
              fillRule="evenodd"
              fill="#707070"
              fillOpacity="1"
            />
          </g>
          <g transform="matrix(-0.7071067690849304,0.7071067690849304,-0.7071067690849304,-0.7071067690849304,26.17652613365317,-6.156331746622527)">
            <path
              d="M14.86328325,3.343168166015625L14.36328125,3.343170166015625L14.36328125,2.343170166015625L14.86328325,2.343172430985625L30.86328125,2.343172430985625L31.36328125,2.343170166015625L31.36328125,3.343170166015625L30.86328125,3.343168166015625L14.86328325,3.343168166015625Z"
              fillRule="evenodd"
              fill="#707070"
              fillOpacity="1"
            />
          </g>
        </g>
      </svg>
    </>
  )
}

//日历图标
const DateIconSvg: FC<IconProps> = (props) => {
  return (
    <>
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        version="1.1"
        width="16"
        height="16"
        viewBox="0 0 16 16"
      >
        <defs>
          <clipPath id="master_svg0_237_70228/154_66356/154_65592/154_65430">
            <rect x="0" y="0" width="16" height="16" rx="0" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_237_70228/154_66356/154_65592/154_65430)">
          <g>
            <path
              d="M5.1666240234375,4.66667L5.1666240234375,2L6.1666240234375,2L6.1666240234375,4.66667L5.1666240234375,4.66667ZM9.8333740234375,4.66667L9.8333740234375,2L10.8333740234375,2L10.8333740234375,4.66667L9.8333740234375,4.66667ZM13.3333740234375,2.833313L11.5000440234375,2.833313L11.5000440234375,3.83331L13.1666740234375,3.83331L13.1666740234375,5.5L2.8333740234375,5.5L2.8333740234375,3.83331L4.5000440234375,3.83331L4.5000440234375,2.833313L2.6667070234375,2.833313Q2.3215290234375,2.833313,2.0774520234375,3.0773900000000003Q1.8333740234375,3.3214699999999997,1.8333740234375,3.6666499999999997L1.8333740234375,13Q1.8333740234375,13.3452,2.0774520234375,13.5892Q2.3215290234375,13.8333,2.6667080234375,13.8333L13.3333740234375,13.8333Q14.1666740234375,13.8333,14.1666740234375,13L14.1666740234375,3.6666499999999997Q14.1666740234375,2.833313,13.3333740234375,2.833313ZM2.8333740234375,6.5L2.8333740234375,12.8333L13.1666740234375,12.8333L13.1666740234375,6.5L2.8333740234375,6.5ZM6.8333640234375,3.83331L9.1667040234375,3.83331L9.1667040234375,2.833313L6.8333640234375,2.833313L6.8333640234375,3.83331ZM5.0000040234375,9.16669L11.0000040234375,9.16669L11.0000040234375,8.16669L5.0000040234375,8.16669L5.0000040234375,9.16669ZM5.0000040234375,11.16669L11.0000040234375,11.16669L11.0000040234375,10.16669L5.0000040234375,10.16669L5.0000040234375,11.16669Z"
              fillRule="evenodd"
              fill="#19191A"
              fillOpacity="1"
            />
          </g>
        </g>
      </svg>
    </>
  )
}

// 渲染日历内容
const RenderContent: FC<RenderContentProps> = (props) => {
  const { active, onChange, startTime = '', endTime = '' } = props

  const [selectDate, setSelectDate] = useState({
    startTime: '',
    endTime: '',
  })

  //选中事件
  const handleSelect = (key: 'startTime' | 'endTime', value: string) => {
    setSelectDate((pre) => ({ ...pre, [key]: value }))
    if (onChange) {
      onChange(key, value)
    }
  }
  useEffect(() => {
    //处理清空事件
    if (startTime && endTime) {
      setSelectDate({ startTime: '', endTime: '' })
    }
  }, [startTime, endTime])

  if (!active) return null

  return (
    <>
      <div className="shadow-[0px_3px_6px_-2px_rgba(0, 0, 0, 0.1)] absolute left-0 top-[50px] z-10 grid min-h-[314px] min-w-[559px] grid-cols-2 rounded-[4px] border border-[#E5E5E5] bg-white">
        <div className="border-r-[1px] border-[#E5E5E5]">
          <RenderDate
            value={startTime}
            onchange={(val) => handleSelect('startTime', val)}
          />
        </div>
        <div>
          <RenderDate
            value={endTime}
            min={selectDate.startTime || ''}
            onchange={(val) => handleSelect('endTime', val)}
          />
        </div>
      </div>
    </>
  )
}

const DatePicker: FC<DatePickerProps> = (props) => {
  const {
    label = '',
    startTime = '',
    endTime = '',
    placeholder = '',
    onChange,
  } = props
  //是否展开选择状态
  const [active, setActive] = useState(false)

  //定义存储已选择的数据
  const [currentDate, setCurrentDate] = useState({
    startTime: startTime,
    endTime: endTime,
  })

  useEffect(() => {
    //清空数据
    if (!startTime && !endTime) {
      setCurrentDate({
        startTime: '',
        endTime: '',
      })
    }
  }, [startTime, endTime])

  //定义一个变量暂时存储已选择的数据
  const [selectDate, setSelectDate] = useState({
    startTime: '',
    endTime: '',
  })

  const handelDateChange = (key: 'startTime' | 'endTime', value: string) => {
    //当点击的是startTime，且endTIme存在时，清空endTime
    if (key === 'startTime' && selectDate['endTime']) {
      setSelectDate((pre) => {
        return { ...pre, [key]: value, endTime: '' }
      })
    } else {
      setSelectDate((pre) => {
        return { ...pre, [key]: value }
      })
      const obj = { ...selectDate, [key]: value }
      //当处于active时，且start和end都已经选择了，此时关闭日期选择区域
      if (active && obj.startTime && obj.endTime) {
        //  同步数据到current
        setCurrentDate((pre) => {
          return { ...obj }
        })
        setActive(false)
        if (onChange) {
          onChange(obj)
        }
      }
    }
  }

  //是否展示close图标
  const [closeShow, setCloseShow] = useState(false)

  const handleMouseEnter = () => {
    if (startTime && endTime) {
      setCloseShow(true)
    }
  }
  const handleMouseLeave = () => {
    setCloseShow(false)
  }

  //定义清除事件
  const handleClear = () => {
    setSelectDate({ startTime: '', endTime: '' })
    setCurrentDate({ startTime: '', endTime: '' })
    if (onChange) {
      onChange({ startTime: '', endTime: '' })
    }
    setActive(false)
  }

  //更新展开状态
  const handleActiveChange = () => {
    setActive(!active)
  }

  // 是否展示 placeholder
  const [showPlaceholder, setShowPlaceholder] = useState(false)

  useEffect(() => {
    setShowPlaceholder(
      placeholder && !currentDate.endTime && !currentDate.endTime
        ? true
        : false,
    )
  }, [placeholder, currentDate])

  return (
    <div className="mb-4">
      {label ? (
        <label className="leading[20px] mb-[4px] block text-[12px] text-[#707070]">
          {label}
        </label>
      ) : null}
      <div className="timePick relative w-[290px]">
        {/* 展示部分 */}
        <div
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          className={`${active ? 'border-[#19191A]' : 'border-transparent'} flex h-[42px] w-full items-center rounded-[3px] border bg-[#F6F6F8] px-[12px] py-[10px] text-[#19191A]`}
        >
          <p
            className={`grid flex-1 cursor-pointer grid-cols-[auto_max-content_auto] gap-x-3 text-[#19191A] ${showPlaceholder ? 'hidden' : ''}`}
            onClick={handleActiveChange}
          >
            <span>{selectDate.startTime}</span>
            <span>-</span>
            <span>{selectDate.endTime}</span>
          </p>
          <p
            onClick={handleActiveChange}
            className={`${showPlaceholder ? '' : 'hidden'} flex-1 text-[13px] text-[#707070]`}
          >
            {placeholder}
          </p>
          <div
            className={`mr-1 flex h-[28px] w-[28px] cursor-pointer rounded-sm ${closeShow ? 'hover:bg-hoverBgRgba' : ''} `}
          >
            <CloseIconSvg
              onClick={handleClear}
              className={`m-auto h-4 w-4 text-[#707070] hover:text-[#19191a] ${closeShow ? '' : 'hidden'}`}
            />
          </div>
          <div className="flex h-[28px] w-[28px] cursor-pointer rounded-sm hover:bg-hoverBgRgba">
            <DateIconSvg
              className="m-auto h-4 w-4"
              onClick={handleActiveChange}
            />
          </div>
        </div>

        {
          <RenderContent
            active={active}
            onChange={handelDateChange}
            {...currentDate}
          ></RenderContent>
        }
      </div>
    </div>
  )
}

export default DatePicker
