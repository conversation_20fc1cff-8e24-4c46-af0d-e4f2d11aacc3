import { FC, useEffect, useState } from 'react'
import {
  DoubleLeftOutlined,
  LeftOutlined,
  RightOutlined,
  DoubleRightOutlined,
} from '@ant-design/icons'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import classNames from 'classnames'

dayjs.extend(isBetween)

interface RenderDateProps {
  value: string
  onchange?: (val: string) => void
  max?: string
  min?: string
  format?: string //日期格式化标准
}

export interface DaysItem {
  value?: string
  disabled: boolean
  selected: boolean //是否选中
  default: boolean //是否是默认的数据
  label: number
}

interface DateObj {
  year: number
  month: number
  day: number
}

interface handleFillDateItemOption {
  date?: string
  max?: string
  min?: string
  defaultValue?: string
  selectDate: string
}
const formateDate = (date: DateObj, format: string = 'MM/DD/YYYY') => {
  const { year, month, day } = date
  if (!(year && month && day)) {
    return ''
  }
  const str = [year, month, day].join('-')
  return dayjs(str).format(format)
}

const RenderDate: FC<RenderDateProps> = (props) => {
  const {
    format,
    max = '2999-12-31',
    min = '1790-01-01',
    onchange,
    value,
  } = props
  // 固定显示的月份
  const weekMap = ['S', 'M', 'T', 'W', 'Th', 'F', 'S']

  // 当前的日期
  const [currentDate, setCurrentDate] = useState({
    year: 1900,
    month: 1,
    day: 1,
  })

  //月份切换是否禁用
  const [yearDisabled, setYearDisabled] = useState({
    pre: false,
    next: false,
  })

  //已选中的日期
  const [selectDate, setSelectDate] = useState('')

  //处理年份年份禁用状态改变
  useEffect(() => {
    // 如果年份超过最大年份，禁用增加年份
    const isYearBiggerMaxYear = currentDate.year >= dayjs(max).year()
    // 如果年份小于最小年份，禁用减少年份
    const isYearLessMinYear = currentDate.year <= dayjs(min).year()

    setYearDisabled({ pre: isYearLessMinYear, next: isYearBiggerMaxYear })
  }, [currentDate.year, max, min])

  //年份切换事件
  const handleYearChange = (type: 1 | -1) => {
    if ((type === 1 && yearDisabled.next) || (type === -1 && yearDisabled.pre))
      return
    const newYear = currentDate.year + type
    setCurrentDate((pre) => ({ ...pre, year: newYear }))
  }

  // 月度切换是否禁用
  const [monthDisabled, setMonthDisabled] = useState({
    pre: false,
    next: false,
  })

  //处理月份年份禁用状态改变
  useEffect(() => {
    // 如果年份超过最大年份，禁用增加年份
    const isMonthBiggerMaxMonth = dayjs(
      `${currentDate.year}-${currentDate.month}-01`,
    ).isAfter(max, 'month')
    // 如果年份小于最小年份，禁用减少年份
    const isMonthLessMinMonth = dayjs(
      `${currentDate.year}-${currentDate.month}-01`,
    ).isBefore(min, 'month')
    setMonthDisabled({ pre: isMonthLessMinMonth, next: isMonthBiggerMaxMonth })
  }, [currentDate.year, currentDate.month, max, min])

  // 月份切换事件
  const handleMonthChange = (type: 1 | -1) => {
    if (
      (type === 1 && monthDisabled.next) ||
      (type === -1 && monthDisabled.pre)
    )
      return
    let newMonth = currentDate.month + type
    let newYear = currentDate.year
    // 处理月份变更时间
    if (newMonth > 12) {
      //当增加的月份超过12
      newYear = newYear + 1
      newMonth = 1
    }
    if (newMonth < 1) {
      //当减少的月份小于1
      newYear = newYear - 1
      newMonth = 12
    }
    //更新数据
    setCurrentDate((pre) => ({ ...pre, year: newYear, month: newMonth }))
  }

  // 月份展示标题
  const [monthTitle, setMonthTitle] = useState<string>('Jan')

  //处理月份显示
  useEffect(() => {
    // 月份map
    enum monthLabelMap {
      'Jan' = 1,
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    }
    setMonthTitle(monthLabelMap[currentDate.month] || 'Jan')
  }, [currentDate.month])

  //处理点击事件
  const handleSelectChange = (target: DaysItem) => {
    if (target.disabled) return
    const value = target.value || ''
    setSelectDate(value)
    // const date=dayjs(value)
    // setCurrentDate({ year: date.year(), month: date.month() + 1, day: date.date() })
    if (onchange) {
      onchange(value as string)
    }
  }

  //定义一个日期数据渲染数据
  const [days, setDays] = useState<DaysItem[]>([])

  //定义一个渲染日期数据的函数
  const handleFillDateItem = (option: handleFillDateItemOption) => {
    const { date, max, min, defaultValue = '', selectDate = '' } = option
    // 如果传入的默认值为空，则获取当前的日期来渲染
    const initDate = date ? dayjs(date) : dayjs()
    const initDateObj = {
      year: initDate.year(),
      month: initDate.month() + 1,
      day: initDate.date(),
    }
    //计算表格中月份的数据
    // 1.计算上个月有多少天
    const lastMonthDays = new Date(
      initDateObj.year,
      initDateObj.month - 1,
      0,
    ).getDate()
    //2.计算本月第一天是星期几
    const firstIsWeek = new Date(
      initDateObj.year,
      initDateObj.month - 1,
      1,
    ).getDay()
    //3.计算本月有多少天
    const currentMonthDays = new Date(
      initDateObj.year,
      initDateObj.month,
      0,
    ).getDate()

    //将currentDate转成字符串
    // const currentDateStr = formateDate(currentDate, 'YYYY-MM-DD')
    const DateRange: DaysItem[] = []
    for (let i = 1; i <= 42; i++) {
      if (i <= firstIsWeek) {
        const current = lastMonthDays - (firstIsWeek - i)
        DateRange.push({
          disabled: true,
          label: current,
          value: formateDate(
            {
              year: initDateObj.year,
              month: initDateObj.month - 1,
              day: current,
            },
            format,
          ),
          // selected:dayjs(`${initDateObj.year}-${initDateObj.month-1}-${current}`).isSame(currentDateStr,'day'),
          selected: dayjs(
            `${initDateObj.year}-${initDateObj.month - 1}-${current}`,
          ).isSame(selectDate, 'day'),
          default: defaultValue
            ? dayjs(
                `${initDateObj.year}-${initDateObj.month - 1}-${current}`,
              ).isSame(defaultValue, 'day')
            : false,
        })
      } else if (i > firstIsWeek && i <= firstIsWeek + currentMonthDays) {
        const currentDay = i - firstIsWeek
        //当当前日期小于min或者大于max时 禁用掉
        const dayDisabledStatus = dayjs(
          `${initDateObj.year}-${initDateObj.month}-${currentDay}`,
        ).isBetween(min, max, 'day', '[]')
        DateRange.push({
          disabled: !dayDisabledStatus,
          label: currentDay,
          value: formateDate(
            {
              year: initDateObj.year,
              month: initDateObj.month,
              day: currentDay,
            },
            format,
          ),
          // selected: dayjs(`${initDateObj.year}-${initDateObj.month}-${currentDay}`).isSame(currentDateStr,'day'),
          selected: dayjs(
            `${initDateObj.year}-${initDateObj.month}-${currentDay}`,
          ).isSame(selectDate, 'day'),
          default: defaultValue
            ? dayjs(
                `${initDateObj.year}-${initDateObj.month}-${currentDay}`,
              ).isSame(defaultValue, 'day')
            : false,
        })
      } else {
        const current = i - (firstIsWeek + currentMonthDays)
        DateRange.push({
          disabled: true,
          label: current,
          value: formateDate(
            {
              year: initDateObj.year,
              month: initDateObj.month + 1,
              day: current,
            },
            format,
          ),
          // selected:dayjs(`${initDateObj.year}-${initDateObj.month+1}-${current}`).isSame(currentDateStr,'day'),
          selected: dayjs(
            `${initDateObj.year}-${initDateObj.month + 1}-${current}`,
          ).isSame(selectDate, 'day'),
          default: defaultValue
            ? dayjs(
                `${initDateObj.year}-${initDateObj.month + 1}-${current}`,
              ).isSame(defaultValue, 'day')
            : false,
        })
      }
    }
    //更新渲染数组
    setDays(DateRange)
  }

  // 当年份和月份改变时重新渲染日期
  useEffect(() => {
    const date = formateDate(currentDate, 'YYYY-MM-DD')
    handleFillDateItem({ date, max, min, defaultValue: value, selectDate })
  }, [currentDate, max, min, selectDate, value])

  //初始化数据
  useEffect(() => {
    // 更新当前的日期
    const date = props.value || dayjs().format('MM/DD/YYYY')
    setCurrentDate({
      year: dayjs(date).year(),
      month: dayjs(date).month() + 1,
      day: dayjs(date).date(),
    })
  }, [props.value])

  return (
    <>
      {/* 头部 */}
      <div className="top grid grid-cols-[28px_28px_auto_28px_28px] border-b-[1px] border-[#E5E5E5] px-3 py-2">
        <DoubleLeftOutlined
          className={`${yearDisabled.pre ? 'cursor-default text-[#ccc]' : 'cursor-pointer text-[#19191a]'}`}
          onClick={() => handleYearChange(-1)}
        />
        <LeftOutlined
          className={`${monthDisabled.pre ? 'cursor-default text-[#ccc]' : 'cursor-pointer text-[#19191a]'}`}
          onClick={() => handleMonthChange(-1)}
        />
        <div className="flex h-8 items-center justify-center text-center text-sm font-semibold text-[#19191a]">
          <span className="min-w-[44px] cursor-pointer px-[6px] py-[7px] hover:bg-hoverBgRgba">
            {monthTitle}
          </span>
          <span className="min-w-[44px] cursor-pointer px-[6px] py-[7px] hover:bg-hoverBgRgba">
            {currentDate.year}
          </span>
        </div>
        <RightOutlined
          className={`${monthDisabled.next ? 'cursor-default text-[#ccc]' : 'cursor-pointer text-[#19191a]'}`}
          onClick={() => handleMonthChange(1)}
        />
        <DoubleRightOutlined
          className={`${yearDisabled.next ? 'cursor-default text-[#ccc]' : 'cursor-pointer text-[#19191a]'}`}
          onClick={() => handleYearChange(1)}
        />
      </div>
      <div className="month grid grid-cols-7 bg-[#f7f7f7] px-[12px]">
        {/* 星期 */}
        {weekMap.map((item, index) => {
          return (
            <div
              key={`${item}-${index}`}
              className="py-[9px] text-center text-sm font-semibold leading-[22px] text-[#19191a]"
            >
              {item}
            </div>
          )
        })}
      </div>
      {/* day */}
      <div className="grid grid-cols-7 gap-2 px-3 pb-3 pt-1 text-center text-sm leading-[22px]">
        {days.map((item) => {
          return (
            <span
              key={item.value}
              onClick={() => handleSelectChange(item)}
              className={classNames('rounded-[4px] border p-[3px]', {
                'bg-[#4B4B4D] text-white': item.default,
                'cursor-pointer hover:bg-[#F2F2F2]':
                  !item.default && !item.disabled,
                'text-[#DEE0E3]': item.disabled,
                'border-[#19191A]': item.selected,
                'border-transparent': !item.selected,
              })}
            >
              {item.label}
            </span>
          )
        })}
      </div>
    </>
  )
}

export default RenderDate
