import { useState, useRef } from 'react'
import clsx from 'clsx'
import Link from 'next/link'

export interface MenuItemProps {
  /**
   * Item text
   */
  title: string
  /**
   * Item key
   */
  key?: string | number
  /**
   * Item url
   */
  url?: string
  /**
   * Item disabled
   */
  disabled?: boolean
  /**
   * Sub items
   */
  children?: MenuItemProps[]
}

const IconArrow = (props: React.ComponentProps<'svg'>) => {
  return (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="15081"
      width="12"
      height="12"
      {...props}
    >
      <path
        d="M926.826667 313.088L512 727.893333 97.152 313.088l45.269333-45.248L512 637.418667l369.578667-369.578667 45.248 45.226667z"
        p-id="15082"
      ></path>
    </svg>
  )
}

export const MenuItem = ({ title, url, children }: MenuItemProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout>(null)

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setIsOpen(true)
  }

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setIsOpen(false)
    }, 100)
  }

  const hasChildren = children && children.length > 0

  return (
    <div className="relative">
      <Link
        href={url || '#'}
        className={`flex h-[42px] items-center rounded px-3 transition-all hover:bg-[#F7F7F7] ${isOpen && 'bg-[#F7F7F7]'}`}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <span className="relative">
          <span className="z-0 font-semibold opacity-0">{title}</span>
          <span className="absolute left-0 top-0 h-full w-full">{title}</span>
        </span>
        {hasChildren && (
          <IconArrow className={`ml-1 ${isOpen ? 'rotate-180' : ''}`} />
        )}
      </Link>
      {hasChildren && isOpen && (
        <div
          className={`absolute top-[53px] rounded bg-white p-2 text-textColorGray shadow-lg ${
            isOpen ? 'animate-fadeIn' : 'animate-fadeOut'
          }`}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {children.map((item, index) => (
            <Link
              key={`${item.title}-${index}`}
              href={item.url || '#'}
              className={`flex h-9 items-center whitespace-nowrap rounded px-2 transition-all hover:bg-[#F7F7F7] hover:text-textColorBlack ${index > 0 && 'mt-1'} ${item.disabled && 'cursor-not-allowed'}`}
            >
              {item.title}
            </Link>
          ))}
        </div>
      )}
    </div>
  )
}

export interface MenuProps {
  /**
   * Menu items
   */
  items: MenuItemProps[]
  /**
   * className
   */
  className?: string
}

export const Menu = ({ items, className }: MenuProps) => {
  return (
    <div className={clsx('flex items-center lg:space-x-3', className)}>
      {items.map((item) => (
        <MenuItem key={item.title || item.key} {...item} />
      ))}
    </div>
  )
}

export default Menu
