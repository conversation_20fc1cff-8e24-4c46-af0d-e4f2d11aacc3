import { useState, useRef, useEffect } from 'react'
import IconButton from './icon-button'
import FsButton from '../ui/FsButton'
import router from 'next/router'

interface IconProps {
  isLogin: boolean
}

function Icon(props: React.ComponentProps<'svg'> & IconProps) {
  const { isLogin } = props
  return !isLogin ? (
    // <svg
    //   xmlns="http://www.w3.org/2000/svg"
    //   width={24}
    //   height={24}
    //   viewBox="0 0 24 24"
    //   {...props}
    // >
    //   <path
    //     fill="#19191A"
    //     d="M12 2A10.13 10.13 0 0 0 2 12a10 10 0 0 0 4 7.92V20h.1a9.7 9.7 0 0 0 11.8 0h.1v-.08A10 10 0 0 0 22 12A10.13 10.13 0 0 0 12 2M8.07 18.93A3 3 0 0 1 11 16.57h2a3 3 0 0 1 2.93 2.36a7.75 7.75 0 0 1-7.86 0m9.54-1.29A5 5 0 0 0 13 14.57h-2a5 5 0 0 0-4.61 3.07A8 8 0 0 1 4 12a8.1 8.1 0 0 1 8-8a8.1 8.1 0 0 1 8 8a8 8 0 0 1-2.39 5.64"
    //   ></path>
    //   <path
    //     fill="#19191A"
    //     d="M12 6a3.91 3.91 0 0 0-4 4a3.91 3.91 0 0 0 4 4a3.91 3.91 0 0 0 4-4a3.91 3.91 0 0 0-4-4m0 6a1.91 1.91 0 0 1-2-2a1.91 1.91 0 0 1 2-2a1.91 1.91 0 0 1 2 2a1.91 1.91 0 0 1-2 2"
    //   ></path>
    // </svg>
    <span className="iconfont text-[20px]">&#xe679;</span>
  ) : (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      // xmlns:xlink="http://www.w3.org/1999/xlink"
      fill="none"
      version="1.1"
      width="24"
      height="24"
      viewBox="0 0 24 24"
    >
      <g>
        <g>
          <g>
            <path
              d="M4.4443359375,18.55052844390869L4.4443359375,18.666628443908692L6.1201859375000005,18.666628443908692L6.1201859375000005,18.55052844390869C6.1201859375000005,17.65922844390869,6.1249259375000005,17.329728443908692,6.1770759375,17.07252844390869C6.4117459375,15.933628443908692,7.3290759375,15.04832844390869,8.4952959375,14.823128443908692C8.758405937500001,14.772128443908692,9.0950059375,14.768528443908691,10.0170759375,14.768528443908691L11.9086359375,14.768528443908691C12.8307059375,14.768528443908691,13.1672959375,14.772128443908692,13.4304059375,14.823128443908692C14.5966359375,15.04832844390869,15.5139359375,15.933628443908692,15.7486359375,17.07252844390869C15.8007359375,17.33092844390869,15.8055359375,17.65922844390869,15.8055359375,18.55052844390869L15.8055359375,18.666628443908692L17.4813359375,18.666628443908692L17.4813359375,18.450928443908694C17.4813359375,17.69242844390869,17.4813359375,17.19462844390869,17.3924359375,16.760828443908693C17.0286359375,14.990228443908691,15.5981359375,13.59998844390869,13.7598959375,13.245618443908691C13.3118959375,13.159098443908691,12.7951459375,13.159098443908691,12.0141159375,13.159098443908691L9.9115959375,13.159098443908691C9.130555937499999,13.159098443908691,8.6138159375,13.159098443908691,8.1658159375,13.245618443908691C6.3275959375,13.59998844390869,4.8970759375,14.990228443908691,4.5332240375,16.760828443908693C4.4443359375,17.193428443908694,4.4443359375,17.69242844390869,4.4443359375,18.44972844390869L4.4443359375,18.55052844390869ZM13.2016659375,8.229908443908691C13.2016659375,9.438798443908691,12.1918959375,10.40709844390869,10.962855937499999,10.40709844390869C9.733815937500001,10.40709844390869,8.7240359375,9.438798443908691,8.7240359375,8.229908443908691C8.7240359375,7.021018443908691,9.733815937500001,6.053908443908691,10.962855937499999,6.053908443908691C12.1918959375,6.053908443908691,13.2016659375,7.021018443908691,13.2016659375,8.229908443908691ZM10.962855937499999,12.01538844390869C13.1317459375,12.01538844390869,14.8775359375,10.314648443908691,14.8775359375,8.229908443908691C14.8775359375,6.146358443908691,13.1317459375,4.444428443908691,10.962855937499999,4.444428443908691C8.793965937500001,4.444428443908691,7.0481859374999996,6.146358443908691,7.0481859374999996,8.229908443908691C7.0481859374999996,10.314648443908691,8.793965937500001,12.01538844390869,10.962855937499999,12.01538844390869Z"
              fill="#19191A"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M8.110471875,5.470817734375Q6.9300218749999996,6.614517734375,6.9300218749999996,8.231107734375Q6.9300218749999996,9.846517734375,8.110471875,10.990227734375Q9.290911874999999,12.135117734375001,10.963211874999999,12.135117734375001Q12.636691875,12.135117734375001,13.817131875,10.990227734375Q14.997571875,9.846517734375,14.997571875,8.231107734375Q14.997571875,6.614517734375,13.817131875,5.470817734375Q12.636691875,4.325927734375,10.963211874999999,4.325927734375Q9.292101875,4.325927734375,8.110471875,5.470817734375ZM8.276391875,10.819557734375Q7.167061875,9.745777734375,7.167061875,8.231107734375Q7.167061875,6.715257734375,8.275211875,5.641487734375Q9.386911875,4.562964734375,10.963211874999999,4.562964734375Q12.539501875,4.562964734375,13.652391875,5.641487734375Q14.760571875,6.715257734375,14.760571875,8.231107734375Q14.760571875,9.745777734375,13.652391875,10.819557734375Q12.540691875,11.898077734375,10.963211874999999,11.898077734375Q9.386911875,11.898077734375,8.275211875,10.819557734375L8.276391875,10.819557734375ZM12.626021875,6.603857734375Q11.936241875,5.935407734375,10.963211874999999,5.935407734375Q9.991351875,5.935407734375,9.300391874999999,6.603857734375Q8.605871875,7.275857734375,8.605871875,8.231107734375Q8.605871875,9.185187734374999,9.300391874999999,9.857187734375Q9.991351875,10.525627734375,10.963211874999999,10.525627734375Q11.935061874999999,10.525627734375,12.626021875,9.857187734375Q13.320541875,9.185187734374999,13.320541875,8.231107734375Q13.320541875,7.275857734375,12.626021875,6.603857734375ZM9.465131875,6.774517734374999Q10.087351875,6.172447734375,10.963211874999999,6.172447734375Q11.840241875,6.172447734375,12.461281875,6.774517734374999Q13.083501875,7.376597734375,13.083501875,8.231107734375Q13.083501875,9.084447734375,12.461281875,9.686517734375Q11.840241875,10.288597734375,10.963211874999999,10.288597734375Q10.086171875,10.288597734375,9.465131875,9.686517734375Q8.842911875,9.084447734375,8.842911875,8.231117734375001Q8.842911875,7.377777734375,9.465131875,6.773337734375L9.465131875,6.774517734374999ZM4.326171875,18.450927734375L4.326171875,18.785227734375L6.240241875,18.785227734375L6.240241875,18.550527734375002Q6.240241875,17.362927734375,6.294761875,17.097527734375Q6.461871875,16.279727734375,7.0698718750000005,15.690627734375Q7.677871875,15.101627734375,8.518171875,14.940427734375Q8.791951874999999,14.887127734375,10.017431875,14.887127734375L11.911361875,14.887127734375Q13.135651875,14.887127734375,13.409431875,14.940427734375Q14.250911875,15.101627734375,14.858871875,15.690627734375Q15.466871875,16.279727734375,15.634071875,17.097527734375Q15.688571875,17.361827734374998,15.688571875,18.550527734375002L15.688571875,18.785227734375L17.600271875,18.785227734375L17.600271875,18.449727734375Q17.600271875,17.181627734375,17.510171874999997,16.738327734374998Q17.229271875000002,15.373027734375,16.212371875000002,14.386927734375Q15.195471875,13.402077734375,13.782761875,13.129477734375Q13.322911875,13.040597734375,12.015651875,13.040597734375L9.911951875,13.040597734375Q8.603501875,13.040597734375,8.143651875,13.129477734375Q6.732101875,13.402077734375,5.714021875,14.386927734375Q4.698318875,15.373027734375,4.417430375,16.738327734374998Q4.32617094817,17.182827734375,4.326171875,18.450927734375ZM4.563208875,18.548127734375L4.563208875,18.450927734375Q4.563208875,17.206527734375,4.649726875,16.785727734375Q4.915208875,15.491527734375,5.879951875,14.557627734375Q6.845871875,13.621337734375,8.188691875,13.361777734375Q8.627211875,13.278817734375,9.911951875,13.278817734375L12.015651875,13.278817734375Q13.300391875,13.278817734375,13.738911875,13.361777734375Q15.081771875,13.621337734375,16.047671875,14.557627734375Q17.012371875,15.491527734375,17.277871875000002,16.785727734375Q17.364371875,17.206527734375,17.363171875,18.449727734375L17.363171875,18.548127734375L15.924371875,18.548127734375Q15.924371875,17.338027734375,15.865171875,17.048927734375Q15.682571875,16.160027734375,15.022471875,15.520027734375Q14.364671875,14.882327734375,13.453281875,14.706927734375Q13.156981875,14.650027734375,11.910171875,14.650027734375L10.017431875,14.650027734375Q8.769431875,14.650027734375,8.474321875000001,14.706927734375Q7.562911875,14.882327734375,6.905131875,15.520027734375Q6.244981875,16.160027734375,6.062471875,17.048927734375Q6.003211875,17.338027734375,6.003211875,18.548127734375L4.563208875,18.548127734375Z"
              fill="#19191A"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M21.1556,14.9985Q21.9259,13.0548,21.9259,10.963Q21.9259,8.8723,21.1556,6.92741Q20.32,4.81659,18.7141,3.21067Q17.1105,1.60593,14.9997,0.77037Q13.0548,0,10.963,0Q8.8723,0,6.92741,0.77037Q4.81659,1.60593,3.21067,3.21185Q1.60593,4.81541,0.77037,6.92622Q0,8.87111,0,10.963Q0,13.0536,0.77037,14.9985Q1.60593,17.1093,3.21185,18.7153Q4.81659,20.32,6.92741,21.1567Q8.87111,21.9259,10.963,21.9259Q13.0536,21.9259,14.9985,21.1556Q17.1093,20.32,18.7153,18.7141Q20.32,17.1093,21.1567,14.9985L21.1556,14.9985ZM19.5034,7.58163Q20.1481,9.21126,20.1481,10.963Q20.1481,12.7147,19.5034,14.3443Q18.803,16.1126,17.4578,17.4578Q16.1126,18.803,14.3443,19.5034Q12.7147,20.1481,10.963,20.1481Q9.21126,20.1481,7.58163,19.5034Q5.81333,18.803,4.46815,17.4578Q3.12296,16.1126,2.42252,14.3443Q1.77778,12.7147,1.77778,10.963Q1.77778,9.21126,2.42252,7.58163Q3.12296,5.81333,4.46815,4.46815Q5.81333,3.12296,7.58163,2.42252Q9.21126,1.77778,10.963,1.77778Q12.7147,1.77778,14.3443,2.42252Q16.1126,3.12296,17.4578,4.46815Q18.803,5.81333,19.5034,7.58163Z"
              fill="#19191A"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M23.407628125000002,19.259233984375Q23.407628125000002,19.463083984375,23.387478125,19.665753984375Q23.367328125,19.868413984375,23.328218125,20.068713984375Q23.287918124999997,20.267823984375,23.228658125000003,20.463383984375Q23.169398125,20.658933984375,23.092368125,20.846193984375Q23.014138125,21.035823984375,22.918138125,21.213603984375Q22.822138125000002,21.394933984375,22.708368125,21.563233984375Q22.595768125,21.732713984375,22.466588125,21.890343984375Q22.336218125000002,22.047973984375,22.192808125,22.192563984375Q22.048218125,22.335973984375002,21.890588125,22.465153984375Q21.732958125,22.595523984375,21.563478125,22.708123984375Q21.395178125,22.821893984375002,21.215028125,22.917893984375Q21.034878125,23.012713984375,20.847628125,23.092123984375Q20.657998125,23.169153984375,20.463628125,23.228413984375003Q20.268068125,23.287673984374997,20.068958125,23.327973984375Q19.868658125,23.367083984375,19.665998125,23.387233984375Q19.463328125,23.407383984375002,19.259478125,23.407383984375002Q19.055628125,23.407383984375002,18.852958125,23.387233984375Q18.650288125,23.367083984375,18.449998125,23.327973984375Q18.250888125,23.287673984374997,18.055328125,23.228413984375003Q17.859768125,23.169153984375,17.672518125,23.092123984375Q17.482888125,23.013893984375,17.305108125,22.917893984375Q17.123778125,22.821893984375002,16.954288125,22.708123984375Q16.785998125,22.595523984375,16.628368125,22.466343984375Q16.470738125,22.335973984375002,16.326148125,22.192563984375Q16.182738125,22.047973984375,16.053553125,21.890343984375Q15.923183125,21.732713984375,15.810590125000001,21.563233984375Q15.696812125,21.394933984375,15.600813125,21.214783984375Q15.505998125,21.034633984375,15.426591125,20.847383984375Q15.349555125,20.657753984375,15.290295125,20.463383984375Q15.231036125,20.267823984375,15.190739825,20.068713984375Q15.151628525,19.868413984375,15.131480225,19.665753984375Q15.11133183232,19.463083984375,15.111328125,19.259233984375Q15.111328125,19.055383984375,15.131476525,18.852713984375Q15.151624825,18.650043984375,15.190736125,18.449753984375Q15.231033125,18.250643984375,15.290291125,18.055083984375Q15.349551125,17.859523984375,15.426587125,17.672273984375Q15.504810125,17.482643984375,15.600809125,17.304863984375Q15.696808125,17.123533984375,15.810586125,16.954043984375Q15.923179125,16.785753984375,16.052365125,16.628123984375Q16.182738125,16.470493984375,16.326138125,16.325903984375Q16.470738125,16.182493984375,16.628368125,16.053308984375Q16.785998125,15.922938984375,16.955478125,15.810345984375001Q17.123768124999998,15.696567984375,17.303918125,15.600568984375Q17.484068125,15.505753984375,17.671328125,15.426346984375Q17.860958125,15.349310984375,18.055328125,15.290050984375Q18.250888125,15.230791984375,18.449998125,15.190495684375Q18.650288125,15.151384384375,18.852958125,15.131236084375Q19.055628125,15.111087691695,19.259478125,15.111083984375Q19.463328125,15.111083984375,19.665998125,15.131232384375Q19.868658125,15.151380684375,20.068958125,15.190491984375Q20.268068125,15.230788984375,20.463628125,15.290046984375Q20.659178125,15.349306984375,20.846438125,15.426342984375Q21.036068125,15.504565984375,21.213848125,15.600564984375Q21.395178125,15.696563984375,21.563478125,15.810341984375Q21.732958125,15.922934984375,21.890588125,16.052120984375Q22.048218125,16.182493984375,22.192808125,16.325893984375Q22.336218125000002,16.470493984375,22.465398125,16.628123984375Q22.595768125,16.785753984375,22.708368125,16.955233984375Q22.822138125000002,17.123523984374998,22.918138125,17.303673984375Q23.012958125,17.483823984375,23.092368125,17.671083984375Q23.169398125,17.860713984375,23.228658125000003,18.055083984375Q23.287918124999997,18.250643984375,23.328218125,18.449753984375Q23.367328125,18.650043984375,23.387478125,18.852713984375Q23.407628125000002,19.055383984375,23.407628125000002,19.259233984375Z"
              fill="#10A300"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M22.6109946875,22.610956540527344Q24.000034687499998,21.223106540527343,24.000034687499998,19.259256540527343Q24.000034687499998,17.295406540527345,22.6109946875,15.907556540527343Q21.2231446875,14.518516540527344,19.2592946875,14.518516540527344Q17.2954446875,14.518516540527344,15.9075946875,15.907556540527343Q14.5185546875,17.295406540527345,14.5185546875,19.259256540527343Q14.5185546875,21.223106540527343,15.9075946875,22.610956540527344Q17.2954446875,23.99999654052734,19.2592946875,23.99999654052734Q21.2231446875,23.99999654052734,22.6109946875,22.610956540527344ZM21.7730746875,16.744296540527344Q22.814854687500002,17.787256540527345,22.814854687500002,19.259256540527343Q22.814854687500002,20.732446540527345,21.7730746875,21.773036540527343Q20.7324746875,22.814816540527346,19.2592946875,22.814816540527346Q17.7861046875,22.814816540527346,16.7455146875,21.773036540527343Q15.703734687499999,20.732446540527345,15.703734687499999,19.259256540527343Q15.703734687499999,17.786076540527343,16.7455146875,16.745476540527342Q17.7861046875,15.703706540527344,19.2592946875,15.703706540527344Q20.7324746875,15.703706540527344,21.7730746875,16.745476540527342L21.7730746875,16.744296540527344Z"
              fill="#FFFFFF"
              fill-opacity="1"
            />
          </g>
        </g>
      </g>
    </svg>
  )
}
interface UserInfoProps {
  userInfo: any
  logout: () => void
}

export function UserInfo(props: UserInfoProps) {
  const { userInfo, logout } = props
  const toRegister = () => {
    router.push('/register')
  }
  const toLogin = () => {
    window.location.href = `${process.env.NEXT_PUBLIC_LOGIN_URL}/login.html?redirect=${window.location.href}&source=api`
  }
  const handleLogout = () => {
    logout()
  }
  return (
    <div className="relative rounded-lg bg-white p-4 text-center">
      {userInfo ? (
        <>
          <div className="mb-2 ml-auto mr-auto size-12 overflow-hidden rounded-full">
            <img
              className="h-[48px] w-[48px] rounded-full"
              src={userInfo.user.customer_photo}
              alt="User"
            />
          </div>
          <div className="text-base font-semibold text-textColorBlack">
            {userInfo.user.customers_firstname}{' '}
            {userInfo.user.customers_lastname}
          </div>
          <div className="text-xs">{userInfo.user.customers_email_address}</div>
          {userInfo.isCompanyOrganizationUser && (
            <div className="mt-2 flex items-center justify-center text-xs">
              <span className="rounded bg-[#F2F2F2] px-1 py-[2px]">
                Business Account
              </span>
            </div>
          )}
          <div className="my-4 border-t border-solid border-[#E5E5E5]"></div>
          <FsButton type="gray" onClick={handleLogout} className="w-full">
            Log out
          </FsButton>
        </>
      ) : (
        <div className="flex flex-col gap-2">
          <FsButton onClick={toLogin}>Sign in</FsButton>
          <FsButton type="gray" onClick={toRegister}>
            Sign up
          </FsButton>
        </div>
      )}
    </div>
  )
}

export function IconUser(props: UserInfoProps) {
  const { userInfo, logout } = props
  const [isOpen, setIsOpen] = useState(false)
  const [isLogin, setIsLogin] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout>(null)
  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setIsOpen(true)
  }

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setIsOpen(false)
    }, 300)
  }
  useEffect(() => {
    if (userInfo) {
      setIsLogin(true)
    } else {
      setIsLogin(false)
    }
  }, [userInfo])

  return (
    <IconButton onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
      <Icon isLogin={isLogin} />
      {isOpen && (
        <div className="absolute right-[0px] top-[54px] w-[320px] cursor-auto text-left">
          <div className="relative rounded-lg shadow-[0_0_15px_rgba(0,0,0,0.2)]">
            <div className="absolute right-[14px] top-[-8px] h-4 w-4 rotate-45 bg-white shadow-[3px_-3px_5px_-1px_rgba(0,0,0,0.1)]"></div>
            <UserInfo userInfo={userInfo} logout={logout} />
          </div>
        </div>
      )}
    </IconButton>
  )
}

export default IconUser
