import React, { useState } from 'react'
import IconButton from './icon-button'
import Drawer from '../drawer'
import { SideMenus } from '../docs'
import Search from './search'

const Icon = (props: React.ComponentProps<'svg'>) => {
  return (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="15251"
      width="24"
      height="24"
      {...props}
    >
      <path
        d="M896 266.666667H128v-64h768v64zM896 544H128v-64h768v64zM896 821.333333H128v-64h768v64z"
        p-id="15252"
      ></path>
    </svg>
  )
}

export function IconMenu({ className }: { className?: string }) {
  const [open, setOpen] = useState(false)
  return (
    <>
      <IconButton className={className} onClick={() => setOpen(true)}>
        <Icon />
      </IconButton>
      <Drawer visible={open} onClose={() => setOpen(false)}>
        <div className="mb-5 w-full bg-[#FAFAFB]">
          <Search placeholder="Search" onSearch={() => setOpen(false)} />
        </div>
        <SideMenus
          onClick={() => {
            setOpen(false)
          }}
        />
      </Drawer>
    </>
  )
}

export default IconMenu
