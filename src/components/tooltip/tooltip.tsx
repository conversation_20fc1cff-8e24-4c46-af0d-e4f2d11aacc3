import classNames from 'classnames'
import { FC, useState, ReactNode } from 'react'

type placementType = 'top' | 'bottom' | 'left' | 'right'

export interface TooltipProps {
  children: ReactNode // 接收一个函数，用以传递触发器组件的props
  text: ReactNode // Popover的内容
  position?: placementType // Popover的位置
}

const Tooltip: FC<TooltipProps> = ({ text, children, position = 'right' }) => {
  // 控制气泡的显示和隐藏，例如通过鼠标悬停显示
  const [isOpen, setIsOpen] = useState(false)

  const handleMouseEnter = () => setIsOpen(true)
  const handleMouseLeave = () => setIsOpen(false)

  // 根据position调整气泡的位置样式类
  const positionClassName = (position: placementType) => {
    let className = ''
    switch (position) {
      case 'top':
        className = 'bottom-full left-[50%] translate-x-[-50%]'
        break
      case 'bottom':
        className = 'top-full left-[50%]'
        break
      case 'left':
        className = 'right-full top-[50%]'
        break
      case 'right':
        className = 'left-full top-[50%]'
        break
    }
    return className
  }

  return (
    <div className="relative inline-block">
      <div
        className="tooltip-trigger"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {children}
      </div>
      {isOpen && (
        <div
          className={classNames(
            `absolute text-[#707070] ${positionClassName(position)}`,
            { 'mb-[15px] -translate-x-[50%]': position === 'top' },
            { 'mt-[15px] -translate-x-[50%]': position === 'bottom' },
            { 'mr-[15px] -translate-y-[50%]': position === 'left' },
            { 'ml-[15px] -translate-y-[50%]': position === 'right' },
          )}
        >
          <div
            className={classNames(
              'absolute border-[8px] border-[transparent]',
              {
                'left-[50%] top-full -translate-x-[50%] border-t-[#fff]':
                  position === 'top',
              },
              {
                'bottom-full left-[50%] -translate-x-[50%] border-b-[#fff]':
                  position === 'bottom',
              },
              {
                'left-full top-[50%] -translate-y-[50%] border-l-[#fff]':
                  position === 'left',
              },
              {
                'right-full top-[50%] -translate-y-[50%] border-r-[#fff]':
                  position === 'right',
              },
            )}
          ></div>
          <div className="w-[max-content] max-w-[246px] rounded-[3px] bg-[#fff] p-5 text-[13px] shadow-tips-shadow">
            {text}
          </div>
        </div>
      )}
    </div>
  )
}

export default Tooltip
