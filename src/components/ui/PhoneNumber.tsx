import React, { useState, useRef, useEffect } from 'react'

interface Option {
  value: string
  label: string
  searchLabel?: string
}

interface PhoneNumberProps {
  label?: string
  error?: string
  required?: boolean
  className?: string
  areaCode: string
  phoneNumber: string
  areaCodeOptions: Option[]
  onAreaCodeChange: (value: string) => void
  onPhoneNumberChange: (value: string) => void
  onBlur?: () => void
}

export const PhoneNumber: React.FC<PhoneNumberProps> = ({
  label,
  error,
  required,
  className = '',
  areaCode,
  phoneNumber,
  areaCodeOptions,
  onAreaCodeChange,
  onPhoneNumberChange,
  onBlur,
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [selectedOption, setSelectedOption] = useState<Option | undefined>(
    areaCodeOptions.find((opt) => opt.value === areaCode),
  )
  const selectRef = useRef<HTMLDivElement>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
        onBlur?.()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [onBlur])

  useEffect(() => {
    setSelectedOption(areaCodeOptions.find((opt) => opt.value === areaCode))
  }, [areaCode, areaCodeOptions])

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [isOpen])

  const handleOptionClick = (option: Option) => {
    setSelectedOption(option)
    setIsOpen(false)
    setSearchValue('')
    onAreaCodeChange(option.value)
    onBlur?.()
  }
  const filteredOptions = areaCodeOptions.filter((option) =>
    (option.searchLabel || option.label)
      .toLowerCase()
      .includes(searchValue.toLowerCase().trim()),
  )

  // const filteredOptions = areaCodeOptions.filter((option) => {
  //   const searchText = searchValue.toLowerCase().trim()
  //   const optionLabel = option.label.toLowerCase()
  //   const optionValue = option.value?.toLowerCase() || ''
  //   const optionSearchLabel = option.searchLabel?.toLowerCase() || ''

  //   return (
  //     optionLabel.includes(searchText) ||
  //     optionValue.includes(searchText) ||
  //     optionSearchLabel.includes(searchText)
  //   )
  // })

  return (
    <div className="mb-4">
      {label && (
        <label className="leading[20px] mb-[4px] block text-[12px] text-[#707070]">
          {label}
          {required && <span className="ml-1">*</span>}
        </label>
      )}
      <div className="flex">
        <div className="relative w-1/3" ref={selectRef}>
          <div onClick={() => setIsOpen(!isOpen)}>
            <input
              type="text"
              readOnly
              value={selectedOption?.value || ''}
              className={`flex h-[42px] w-full cursor-pointer items-center justify-between rounded-[3px] rounded-r-none border border-[transparent] bg-[#F6F6F8] px-[12px] py-[10px] text-[#19191A] focus:border-[#707070] focus:outline-none ${
                error ? '' : ''
              }`}
            />
            <span
              className={`iconfont absolute right-3 top-[12px] transform cursor-pointer text-[16px] text-[#707070] ${
                isOpen ? 'transform_rotateX_180' : ''
              } transition-transform duration-200`}
            >
              &#xe704;
            </span>
          </div>
          {isOpen && (
            <div className="absolute left-0 right-0 top-[calc(100%+4px)] z-10 max-h-[300px] w-[300%] overflow-hidden rounded-lg border border-[#E5E5E5] bg-white shadow-lg">
              <div className="sticky top-0 bg-white p-[12px]">
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  placeholder="Search your country"
                  className="h-[42px] w-full rounded-[4px] border border-[#E5E5E5] bg-[#fafafb] px-[12px] py-[10px] text-[13px] text-[#19191A] focus:border-[#707070] focus:outline-none"
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
              <div className="max-h-[240px] overflow-y-auto">
                {filteredOptions.map((option) => (
                  <div
                    key={option.searchLabel}
                    className={`cursor-pointer px-4 py-2 hover:bg-[#F5F5F5] ${
                      selectedOption?.value === option.value
                        ? 'bg-[#F0F0F0]'
                        : ''
                    }`}
                    onClick={() => handleOptionClick(option)}
                  >
                    {option.label}
                  </div>
                ))}
                {filteredOptions.length === 0 && (
                  <div className="px-4 py-2 text-[#707070]">
                    No matching results
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        <input
          type="tel"
          value={phoneNumber}
          onChange={(e) => onPhoneNumberChange(e.target.value)}
          onBlur={onBlur}
          className={`h-[42px] w-2/3 rounded-[3px] rounded-l-none border border-[transparent] bg-[#F6F6F8] px-[12px] py-[10px] text-[#19191A] focus:border-[#707070] focus:outline-none ${
            error ? '' : ''
          } ${className}`}
        />
      </div>
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  )
}
