import React from 'react'

interface FsButtonProps {
  type?:
    | 'red'
    | 'black'
    | 'blackline'
    | 'gray'
    | 'whiteline'
    | 'grayline'
    | 'lightgray'
    | 'white'
  round?: boolean
  loading?: boolean
  disabled?: boolean
  text?: string
  htmlType?: 'button' | 'submit'
  onClick?: () => void
  children?: React.ReactNode
  className?: string
}

const getButtonClasses = (
  type: string,
  round: boolean,
  loading: boolean,
  disabled: boolean,
  className?: string,
) => {
  const baseClasses =
    'inline-block relative h-[42px] transition-all duration-300 cursor-pointer rounded px-6 font-normal text-sm leading-[14px]'
  const roundClass = round ? 'rounded-[42px]' : 'rounded'
  const disabledClass = disabled ? 'opacity-30 cursor-not-allowed' : ''
  const loadingClass = loading ? 'opacity-60' : ''
  const beforeClasses =
    'before:content-[""] before:absolute before:inset-0 before:rounded-[inherit] before:bg-[#19191a] before:opacity-0 before:transition-opacity duration-300'
  const hoverClasses = 'hover:before:opacity-20'
  const beforeHoverClasses = `${beforeClasses} ${hoverClasses}`

  const typeClasses = {
    red: 'bg-[#c00000] text-white ' + beforeHoverClasses,
    black: 'bg-[#4b4b4d] text-white hover:before:opacity-60',
    blackline:
      'border border-[#19191a] text-[#19191a] hover:before:opacity-[0.04]',
    gray:
      'bg-[#f2f2f2] text-[#707070] hover:text-[#19191a] hover:before:opacity-[0.04] ' +
      beforeClasses,
    whiteline: 'border border-white text-white hover:before:opacity-[0.04]',
    grayline:
      'border border-[#ccc] text-[#19191a] hover:bg-[rgba(25,25,26,.04)]',
    lightgray: 'bg-[#f2f2f2] text-[#19191a] hover:bg-[#e9e9e9]',
    white: 'bg-[#fff] text-[#707070] hover:bg-[#f2f2f2] hover:text-[#19191a]',
  }[type]

  return `${baseClasses} ${roundClass} ${typeClasses} ${disabledClass} ${loadingClass} ${className || ''}`
}

const FsButton: React.FC<FsButtonProps> = ({
  type = 'red',
  round = false,
  loading = false,
  disabled = false,
  text = '',
  htmlType = 'button',
  onClick,
  children,
  className,
}) => {
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!loading && !disabled && onClick) {
      onClick()
    }
  }

  return (
    <button
      className={getButtonClasses(type, round, loading, disabled, className)}
      disabled={disabled}
      type={htmlType}
      onClick={handleClick}
    >
      <div
        className={`relative flex items-center justify-center whitespace-nowrap ${loading ? 'opacity-0' : 'opacity-100'}`}
      >
        {children}
        {text && <span>{text}</span>}
      </div>
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <svg className="h-4 w-4 animate-spin" viewBox="25 25 50 50">
            <circle
              className={`${type.includes('black') || type === 'gray' || type === 'blackline' || type === 'grayline' ? 'stroke-[#19191a]' : 'stroke-white'} animate-[dash_1.5s_ease-in-out_infinite]`}
              cx="50"
              cy="50"
              r="20"
              fill="none"
              strokeWidth="3"
              strokeMiterlimit="10"
              strokeDasharray="1,200"
              strokeDashoffset="0"
              strokeLinecap="round"
            />
          </svg>
        </div>
      )}
      {/* <div
        className={`absolute inset-0 ${round ? 'rounded-[42px]' : 'rounded'} pointer-events-none bg-[#19191a] opacity-0 transition-opacity duration-300 ${type === 'whiteline' ? 'bg-white' : 'bg-[#19191a]'}`}
      /> */}
    </button>
  )
}

export default FsButton
