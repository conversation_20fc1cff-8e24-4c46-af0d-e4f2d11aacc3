import React, { useState, useRef, useEffect } from 'react'

export interface CountryItem {
  address_format_id: number
  countries_chinese_name: string
  countries_id: number
  countries_name: string
  iso_code: string
  tel_prefix: string
  states: State[]
  citys: string[]
}

export interface State {
  states_code: string
  states: string
  display: number
}

interface CountrySelectProps {
  label?: string
  options: CountryItem[]
  error?: string
  required?: boolean
  value?: number
  onChange?: (value: number) => void
  onStateListChange?: (states: State[]) => void
  onBlur?: () => void
  className?: string
  placeholder?: string
}

export const CountrySelect: React.FC<CountrySelectProps> = ({
  label,
  options,
  error,
  required,
  value,
  onChange,
  onStateListChange,
  onBlur,
  className = '',
  placeholder = 'Please select',
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [selectedOption, setSelectedOption] = useState<CountryItem | undefined>(
    options.find((opt) => opt.countries_id === value),
  )
  const selectRef = useRef<HTMLDivElement>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
        onBlur?.()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [onBlur])

  useEffect(() => {
    setSelectedOption(options.find((opt) => opt.countries_id === value))
  }, [value, options])

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [isOpen])

  const handleOptionClick = (option: CountryItem) => {
    setSelectedOption(option)
    setIsOpen(false)
    setSearchValue('')
    onChange?.(option.countries_id)
    onStateListChange?.(option.states)
    onBlur?.()
  }

  const filteredOptions = options.filter(
    (option) =>
      option.countries_name.toLowerCase().includes(searchValue.toLowerCase()) ||
      option.countries_chinese_name
        .toLowerCase()
        .includes(searchValue.toLowerCase()),
  )

  return (
    <div className="mb-4" ref={selectRef}>
      {label && (
        <label className="leading[20px] mb-[4px] block text-[12px] text-[#707070]">
          {label}
          {required && <span className="ml-1">*</span>}
        </label>
      )}
      <div className="relative">
        <input
          type="text"
          readOnly
          value={selectedOption ? selectedOption.countries_name : ''}
          placeholder={placeholder}
          className={`flex h-[42px] w-full cursor-pointer items-center justify-between rounded-[3px] border border-[transparent] bg-[#F6F6F8] px-[12px] py-[10px] text-[#19191A] focus:border-[#707070] focus:outline-none ${
            error ? 'border-red-500' : ''
          } ${className}`}
          onClick={() => setIsOpen(!isOpen)}
        />
        <span
          className={`iconfont absolute right-3 top-[12px] transform cursor-pointer text-[16px] text-[#707070] ${
            isOpen ? 'transform_rotateX_180' : ''
          } transition-transform duration-200`}
        >
          &#xe704;
        </span>
        {isOpen && (
          <div className="absolute left-0 right-0 top-[calc(100%+4px)] z-10 max-h-[300px] overflow-hidden rounded-lg border border-[#E5E5E5] bg-white shadow-lg">
            <div className="sticky top-0 bg-white p-[12px]">
              <input
                ref={searchInputRef}
                type="text"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                placeholder="Search your country"
                className="h-[42px] w-full rounded-[4px] border border-[#E5E5E5] bg-[#fafafb] px-[12px] py-[10px] text-[13px] text-[#19191A] focus:border-[#707070] focus:outline-none"
                onClick={(e) => e.stopPropagation()}
              />
            </div>
            <div className="max-h-[240px] overflow-y-auto">
              {filteredOptions.map((option) => (
                <div
                  key={option.countries_id}
                  className={`cursor-pointer px-4 py-2 text-[13px] text-[#19191A] hover:bg-[#F5F5F5] ${
                    selectedOption?.countries_id === option.countries_id
                      ? 'bg-[#F0F0F0]'
                      : ''
                  }`}
                  onClick={() => handleOptionClick(option)}
                >
                  {option.countries_name}
                </div>
              ))}
              {filteredOptions.length === 0 && (
                <div className="px-4 py-2 text-[#707070]">
                  No matching results
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  )
}
