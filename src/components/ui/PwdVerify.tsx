import React, {
  useState,
  useRef,
  forwardRef,
  useImperative<PERSON><PERSON>le,
  useEffect,
} from 'react'

interface PwdVerifyProps {
  value: string
  onChange: (value: string) => void
  onFocus?: () => void
  onBlur?: () => void
  showRedBorder?: boolean
  error?: string
  label?: string
  required?: boolean
}

interface VerifyItem {
  msg: string
  isPass: boolean
  verifyFn: (value: string) => boolean
}

export interface PwdVerifyRef {
  validate: () => boolean
  validateWithRules: () => {
    isValid: boolean
    errors: string[]
  }
}

const PwdVerify = forwardRef<PwdVerifyRef, PwdVerifyProps>(
  (
    {
      value,
      onChange,
      onFocus,
      onBlur,
      showRedBorder = false,
      error,
      label,
      required,
    },
    ref,
  ) => {
    const [isEye, setIsEye] = useState(false)
    const [showPopover, setShowPopover] = useState(false)
    const [showEye, setShowEye] = useState(false)
    const inputRef = useRef<HTMLInputElement>(null)
    const popoverRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          showPopover &&
          popoverRef.current &&
          !popoverRef.current.contains(event.target as Node) &&
          !inputRef.current?.contains(event.target as Node)
        ) {
          setShowPopover(false)
        }
      }

      document.addEventListener('mousedown', handleClickOutside)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }, [showPopover])

    const verifyList: VerifyItem[] = [
      {
        msg: 'At least one letter',
        isPass: false,
        verifyFn: (value) => /[a-zA-Z]/.test(value),
      },
      {
        msg: 'At least one number',
        isPass: false,
        verifyFn: (value) => /\d/.test(value),
      },
      {
        msg: 'Length between 6 and 32 characters',
        isPass: false,
        verifyFn: (value) => value.length >= 6 && value.length <= 32,
      },
      {
        msg: 'No spaces',
        isPass: false,
        verifyFn: (value) => !/\s/.test(value),
      },
    ]

    const handlePwdInput = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value
      setShowPopover(newValue.length > 0)
      onChange(newValue)
    }

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setShowPopover(value.length > 0)
      onFocus?.()
    }

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setShowPopover(false)
      onBlur?.()
    }

    useImperativeHandle(ref, () => ({
      validate: () => {
        return verifyList.every((item) => item.verifyFn(value))
      },
      validateWithRules: () => {
        const errors: string[] = []
        verifyList.forEach((item) => {
          if (!item.verifyFn(value)) {
            errors.push(item.msg)
          }
        })
        return {
          isValid: errors.length === 0,
          errors,
        }
      },
    }))

    return (
      <div className="mb-4">
        {label && (
          <label className="leading[20px] mb-[4px] block text-[12px] text-[#707070]">
            {label}
            {required && <span className="ml-1">*</span>}
          </label>
        )}

        <div className="relative">
          <input
            ref={inputRef}
            type={isEye ? 'text' : 'password'}
            value={value}
            onChange={handlePwdInput}
            onFocus={handleFocus}
            onBlur={handleBlur}
            className={`h-[42px] w-full rounded-[3px] border bg-[#F6F6F8] px-[12px] py-[10px] pr-[40px] text-[#19191A] focus:border-[#707070] focus:outline-none ${
              error ? 'border-red-500' : 'border-transparent'
            }`}
            autoComplete="off"
          />

          <button
            type="button"
            className={`absolute right-2 top-1/2 flex h-[28px] w-[28px] -translate-y-1/2 items-center justify-center rounded-[3px] text-[16px] text-[#707070] hover:bg-[rgba(25,25,26,0.04)] hover:text-[#19191A] ${
              isEye ? 'text-[#19191A]' : ''
            }`}
            onFocus={() => setShowEye(true)}
            onBlur={() => setShowEye(false)}
            onClick={() => setIsEye(!isEye)}
          >
            {isEye ? (
              <span className="iconfont">&#xe748;</span>
            ) : (
              <span className="iconfont">&#xe706;</span>
            )}
          </button>
        </div>

        {showPopover && (
          <div
            ref={popoverRef}
            className="absolute z-10 mt-2 w-[400px] rounded-lg bg-white p-4 shadow-lg"
          >
            <ul className="space-y-2">
              {verifyList.map((item, index) => {
                const isPass = item.verifyFn(value)
                return (
                  <li key={index} className="flex items-center">
                    <span
                      className={`mr-2 text-sm ${isPass ? 'text-green-500' : 'text-red-500'}`}
                    >
                      {isPass ? (
                        <span className="iconfont">&#xe710;</span>
                      ) : (
                        <span className="iconfont">&#xf293;</span>
                      )}
                    </span>
                    <p className="text-sm text-gray-600">{item.msg}</p>
                  </li>
                )
              })}
            </ul>
          </div>
        )}

        {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
      </div>
    )
  },
)

PwdVerify.displayName = 'PwdVerify'

export { PwdVerify }
