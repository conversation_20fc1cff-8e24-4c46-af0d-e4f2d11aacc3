import React from 'react'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  required?: boolean
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  required,
  className = '',
  ...props
}) => {
  return (
    <div className="mb-4">
      {label && (
        <label className="leading[20px] mb-[4px] block text-[12px] text-[#707070]">
          {label}
          {required && <span className="ml-1">*</span>}
        </label>
      )}
      <input
        className={`h-[42px] w-full rounded-[3px] border border-[transparent] bg-[#F6F6F8] px-[12px] py-[10px] text-[#19191A] focus:border-[#707070] focus:outline-none ${
          error ? 'border-red-500' : ''
        } ${className}`}
        {...props}
      />
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  )
}
