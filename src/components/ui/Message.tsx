import React, { useState } from 'react'
import { createRoot } from 'react-dom/client'
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons'

export type MessageType = 'success' | 'error' | 'info' | 'warning'

interface MessageProps {
  type: MessageType
  content: string
  duration?: number
  onClose?: () => void
  closable?: boolean
}

// interface MessageInstance extends MessageProps {
//   id: string
// }

const MessageComponent: React.FC<MessageProps & { id: string }> = ({
  type,
  content,
  onClose,
  id,
  closable = false,
}) => {
  const [isVisible, setIsVisible] = useState(true)

  const icons = {
    success: <CheckCircleOutlined className="text-green-500" />,
    error: <CloseCircleOutlined className="text-red-500" />,
    info: <InfoCircleOutlined className="text-blue-500" />,
    warning: <ExclamationCircleOutlined className="text-yellow-500" />,
  }

  const handleClose = () => {
    setIsVisible(false)
    if (onClose) {
      onClose()
    }
    setTimeout(() => {
      const element = document.getElementById(id)
      if (element) {
        element.remove()
      }
    }, 300)
  }

  return (
    <div
      id={id}
      className={`fixed left-1/2 top-8 z-50 -translate-x-1/2 transform transition-all duration-300 ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
    >
      <div className="flex min-w-[320px] items-center gap-2 rounded-lg bg-white px-6 py-4 shadow-lg">
        <span className="text-[16px]">{icons[type]}</span>
        <span className="flex-1 text-[14px] text-gray-700">{content}</span>
        {closable && (
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <CloseCircleOutlined />
          </button>
        )}
      </div>
    </div>
  )
}

let messageContainer: HTMLDivElement | null = null

const createMessageContainer = () => {
  if (!messageContainer) {
    messageContainer = document.createElement('div')
    messageContainer.className = 'message-container'
    document.body.appendChild(messageContainer)
  }
  return messageContainer
}

const message = {
  success(content: string, duration = 3000) {
    this.show({ type: 'success', content, duration })
  },
  error(content: string, duration = 3000) {
    this.show({ type: 'error', content, duration })
  },
  info(content: string, duration = 3000) {
    this.show({ type: 'info', content, duration })
  },
  warning(content: string, duration = 3000) {
    this.show({ type: 'warning', content, duration })
  },
  show({ type, content, duration = 3000, closable = false }: MessageProps) {
    const container = createMessageContainer()
    const id = `message-${Date.now()}`
    const messageDiv = document.createElement('div')
    messageDiv.id = id
    container.appendChild(messageDiv)

    const root = createRoot(messageDiv)
    const handleClose = () => {
      const element = document.getElementById(id)
      if (element) {
        root.unmount()
        element.remove()
      }
    }

    root.render(
      <MessageComponent
        type={type}
        content={content}
        onClose={handleClose}
        id={id}
        closable={closable}
      />,
    )

    if (duration > 0) {
      setTimeout(handleClose, duration)
    }
  },
}

export default message
