import React, { Fragment, useState } from 'react'
import clsx from 'clsx'
import {
  LeftOutlined,
  RightOutlined,
  DoubleRightOutlined,
  DoubleLeftOutlined,
} from '@ant-design/icons'

export interface PaginationProps {
  /**
   * 当前页数
   */
  current: number

  /**
   * 总条数
   */
  total: number

  /**
   * 默认的每页条数
   */
  defaultPageSize?: number

  /**
   * 最多可见页码数
   */
  maxVisiblePages?: number

  /**
   * 禁用分页
   */
  disabled?: boolean

  /**
   * 每页条数
   */
  pageSize?: number

  /**
   * 指定每页可以显示多少条
   */
  pageSizeOptions?: number[]

  /**
   * 是否可以快速跳转至某页
   */
  showQuickJumper?: boolean

  /**
   * 是否展示 pageSize 切换器，当 total 大于 50 时默认为 true
   */
  showSizeChanger?: boolean

  /**
   * 用于显示数据总量和当前数据顺序
   * @param total
   * @param range
   * @returns
   */
  showTotal?: (total: number, range: [number, number]) => React.ReactNode

  /**
   * 是否为简洁模式
   */
  simple?: boolean

  /**
   * 当为 small 时，是小尺寸分页
   */
  size?: 'default' | 'small'

  /**
   * 对齐方式
   */
  align?: 'start' | 'center' | 'end'

  /**
   * 页码或 pageSize 改变的回调，参数是改变后的页码及每页条数
   * @param page
   * @param pageSize
   * @returns
   */
  onChange?: (page: number, pageSize: number) => void

  /**
   * pageSize 变化的回调
   * @param current
   * @param size
   * @returns
   */
  onShowSizeChange?: (current: number, size: number) => void
}

const IconElipse = ({
  stroke = `#19191A`,
  ...props
}: React.SVGProps<SVGSVGElement & { stroke?: string }>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="none"
        stroke={stroke}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1}
        d="M4 12a1 1 0 1 0 2 0a1 1 0 1 0-2 0m7 0a1 1 0 1 0 2 0a1 1 0 1 0-2 0m7 0a1 1 0 1 0 2 0a1 1 0 1 0-2 0"
      ></path>
    </svg>
  )
}

const PageItem = ({
  children,
  disabled,
  className,
  onClick,
}: React.PropsWithChildren<{
  disabled?: boolean
  className?: string
  onClick?: () => void
}>) => {
  return (
    <li
      className={clsx(
        'relative flex size-8 cursor-pointer items-center justify-center rounded-[4px] text-sm transition-all hover:bg-[#F7F7F7]',
        {
          '!cursor-not-allowed text-[#cccccc] text-[rgba(0,0,0,0.25)] hover:bg-transparent':
            disabled,
        },
        className,
      )}
      onClick={onClick}
    >
      {children}
    </li>
  )
}

export function Pagination({
  current,
  total,
  defaultPageSize = 10,
  disabled = false,
  pageSize = defaultPageSize,
  pageSizeOptions = [10, 20, 50, 100],
  showQuickJumper = false,
  showSizeChanger = total > 50,
  showTotal,
  simple = false,
  align = 'end',
  maxVisiblePages = 5,
  onChange,
  onShowSizeChange,
}: PaginationProps) {
  const [currentPage, setCurrentPage] = useState(current)
  const [currentPageSize, setCurrentPageSize] = useState(pageSize)
  const [jumpPage, setJumpPage] = useState('')

  const totalPages = Math.ceil(total / currentPageSize)

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return
    setCurrentPage(page)
    if (onChange) {
      onChange(page, currentPageSize)
    }
  }

  const handlePageSizeChange = (size: number) => {
    setCurrentPageSize(size)
    setCurrentPage(1) // Reset to first page
    if (onShowSizeChange) {
      onShowSizeChange(1, size)
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      handlePageChange(Number(jumpPage))
    }
  }
  const handleBlur = () => {
    setJumpPage('')
  }

  const renderPageNumbers = () => {
    const pages = []

    const startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2))
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)

    // previous page
    pages.push(
      <PageItem
        key="previous"
        disabled={disabled || currentPage === 1}
        onClick={() => handlePageChange(currentPage - 1)}
      >
        <LeftOutlined />
      </PageItem>,
    )

    if (startPage > 1) {
      pages.push(
        <PageItem
          key="startPage"
          disabled={disabled}
          className={clsx({
            'bg-[#F2F2F2] font-semibold hover:!bg-[#F2F2F2]': 1 === currentPage,
          })}
          onClick={() => handlePageChange(1)}
        >
          1
        </PageItem>,
      )
      if (startPage > 2) {
        pages.push(
          <PageItem
            key="startPage_2"
            disabled={disabled}
            className="group hover:!bg-transparent"
            onClick={() =>
              handlePageChange(Math.max(1, currentPage - maxVisiblePages))
            }
          >
            <IconElipse
              className={clsx('transition-all', {
                'group-hover:opacity-0': !disabled,
              })}
              stroke={disabled ? '#cccccc' : '#19191A'}
            />
            <DoubleLeftOutlined className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-white text-[#1677ff] opacity-0 transition-all group-hover:opacity-100" />
          </PageItem>,
        )
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <Fragment key={i}>
          <PageItem
            disabled={disabled}
            className={clsx({
              'bg-[#F2F2F2] font-semibold hover:!bg-[#F2F2F2]':
                i === currentPage,
            })}
            onClick={() => handlePageChange(i)}
          >
            {i}
          </PageItem>
        </Fragment>,
      )
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push(
          <PageItem
            key="endPage"
            disabled={disabled}
            className="group hover:!bg-transparent"
            onClick={() =>
              handlePageChange(
                Math.min(totalPages, currentPage + maxVisiblePages),
              )
            }
          >
            <IconElipse
              className={clsx('transition-all', {
                'group-hover:opacity-0': !disabled,
              })}
              stroke={disabled ? '#cccccc' : '#19191A'}
            />
            {!disabled && (
              <DoubleRightOutlined className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-white text-[#1677ff] opacity-0 transition-all group-hover:opacity-100" />
            )}
          </PageItem>,
        )
      }
      pages.push(
        <PageItem
          key="end"
          disabled={disabled}
          className={clsx({
            'bg-[#F2F2F2] font-semibold hover:!bg-[#F2F2F2]':
              totalPages === currentPage,
          })}
          onClick={() => handlePageChange(totalPages)}
        >
          {totalPages}
        </PageItem>,
      )
    }

    // next page
    pages.push(
      <PageItem
        key="next_page"
        disabled={disabled || currentPage === totalPages}
        onClick={() => handlePageChange(currentPage + 1)}
      >
        <RightOutlined />
      </PageItem>,
    )
    return pages
  }

  return (
    <div
      className={clsx(
        'flex items-center space-x-4 text-textColorBlack',
        {
          'justify-start': align === 'start',
          'justify-center': align === 'center',
          'justify-end': align === 'end',
          'text-[#ccc]': disabled,
        },
        simple && '!space-x-2',
      )}
    >
      {showTotal && (
        <div>
          {showTotal(total, [
            (currentPage - 1) * currentPageSize + 1,
            Math.min(currentPage * currentPageSize, total),
          ])}
        </div>
      )}

      <ul className="flex space-x-2">{renderPageNumbers()}</ul>

      {showSizeChanger && !simple && (
        <div
          className={clsx('rounded-[4px] bg-[#F7F7F7] px-4', [
            disabled ? 'cursor-not-allowed' : 'cursor-pointer',
          ])}
        >
          <select
            value={currentPageSize}
            onChange={(e) => handlePageSizeChange(Number(e.target.value))}
            disabled={disabled}
            className={clsx(
              'h-[32px] border-none bg-transparent outline-none',
              { 'cursor-not-allowed text-[#ccc]': disabled },
            )}
          >
            {pageSizeOptions.map((size) => (
              <option key={size} value={size}>
                {size} / page
              </option>
            ))}
          </select>
        </div>
      )}
      {showQuickJumper && !simple && (
        <>
          <span>go to</span>
          <input
            type="text"
            className={clsx(
              'h-[32px] w-[32px] rounded-[4px] border-none bg-[#F7F7F7] px-2 outline-none',
              { 'cursor-not-allowed': disabled },
            )}
            value={jumpPage}
            onChange={(e) => setJumpPage(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            disabled={disabled}
          />
        </>
      )}
    </div>
  )
}

export default Pagination
