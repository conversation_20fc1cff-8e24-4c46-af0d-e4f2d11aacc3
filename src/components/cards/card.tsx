import Image from 'next/image'
import Link from 'next/link'
import { IMenusItem } from '@/contexts'

export type CardProps = IMenusItem

export function Card({ icon, title, brief, children }: CardProps) {
  return (
    <div className="flex rounded-lg bg-white p-8 shadow-lg">
      {/* svg icon */}
      <span className="mr-4 flex size-9 items-center justify-center">
        <Image src={icon || '/file.svg'} alt="icon" width={36} height={36} />
      </span>
      <div className="relative flex-1">
        <div className="mb-1 text-[14px]/[22px] font-semibold text-textColorBlack">
          {title}
        </div>
        <div className="mb-2 text-[12px]/[20px] text-textColorGray">
          {brief}
        </div>
        <div className="flex flex-col">
          {children &&
            children.length > 0 &&
            children.map((link, index) => {
              return (
                <div key={index} className="mt-3">
                  <Link
                    href={link.url}
                    className="text-[12px]/[20px] text-textColorLink hover:underline"
                  >
                    {link.title}
                  </Link>
                </div>
              )
            })}
        </div>
      </div>
    </div>
  )
}

export default Card
