import React from 'react'
import { DocsSideBar } from './side-bar'
import { DocsSideAnchor, type ISideAnchorItem } from './side-anchor'
import { Breadcrumb, type IBreadcrumbItem } from './breadcrumb'
// import { Pagination } from './pagination'

export interface LayoutProps {
  anchors: ISideAnchorItem[]
  breadcrumbs?: IBreadcrumbItem[]
  noWrap?: boolean
}

const Layout = ({
  anchors,
  breadcrumbs,
  noWrap,
  children,
}: React.PropsWithChildren<LayoutProps>) => {
  if (noWrap) {
    return (
      <div className="relative mx-6 py-6 max-lg:py-4">
        <div className="relative mx-auto max-w-[1200px]">{children}</div>
      </div>
    )
  }
  return (
    <div className="relative lg:flex">
      <div className="sticky top-0 h-[max-content] w-[300px] flex-shrink-0 max-lg:hidden">
        <DocsSideBar />
      </div>

      <div className="relative min-h-[calc(100vh-128px)] flex-1 border-l border-r border-solid border-[#E5E5E5] max-xl:min-h-[calc(100vh-112px)] max-xl:border-r-0 max-lg:border-l-0">
        <div className="relative mx-6 flex flex-col pb-10 pt-6 max-lg:py-4 max-sm:mx-4">
          <Breadcrumb className="mb-3" datas={breadcrumbs} />
          <div className="relative overflow-auto">{children}</div>
          {/* <Pagination /> */}
        </div>
      </div>

      <div className="sticky top-0 h-[max-content] w-[258px] flex-shrink-0 max-xl:hidden">
        <DocsSideAnchor datas={anchors} />
      </div>
    </div>
  )
}

export const WithDocsSide = <P extends LayoutProps>(
  Page: React.ComponentType<P>,
) => {
  const WrappedComponent: React.FC<P> = (props) => {
    return (
      <Layout
        anchors={props?.anchors || []}
        breadcrumbs={props?.breadcrumbs || []}
        noWrap={!!props?.noWrap}
      >
        <Page {...props} />
      </Layout>
    )
  }

  return WrappedComponent
}

export default WithDocsSide
