import clsx from 'clsx'
import Link from 'next/link'
import { Fragment } from 'react'

export interface IBreadcrumbItem {
  title: string
  url?: string
}
export interface IBreadcrumbProps {
  datas?: IBreadcrumbItem[]
  className?: string
}
export function Breadcrumb({
  datas: breadcrumbs,
  className,
}: IBreadcrumbProps) {
  const datas = (breadcrumbs || []).filter((v) => v.title)
  return (
    Array.isArray(datas) &&
    datas.length > 0 && (
      <ol
        className={clsx(
          'flex flex-nowrap items-center gap-1 overflow-x-auto text-xs/5',
          className,
        )}
      >
        <li className="shrink-0">
          <Link href="/">Home</Link>
        </li>
        {datas.map((data, index) => {
          return (
            <Fragment key={index}>
              <li className="shrink-0">
                <span className="px-1">/</span>
              </li>
              <li className="shrink-0">
                {index === datas.length - 1 || !data.url ? (
                  <span className="text-textColorBlack">{data.title}</span>
                ) : (
                  <Link href={data.url}>{data.title}</Link>
                )}
              </li>
            </Fragment>
          )
        })}
      </ol>
    )
  )
}

export default Breadcrumb
