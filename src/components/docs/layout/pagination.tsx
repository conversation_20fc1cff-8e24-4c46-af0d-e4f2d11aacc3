import Link from 'next/link'

function Icon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={20}
      height={20}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="#19191A"
        d="M16.15 13H5q-.425 0-.712-.288T4 12t.288-.712T5 11h11.15L13.3 8.15q-.3-.3-.288-.7t.288-.7q.3-.3.713-.312t.712.287L19.3 11.3q.15.15.213.325t.062.375t-.062.375t-.213.325l-4.575 4.575q-.3.3-.712.288t-.713-.313q-.275-.3-.288-.7t.288-.7z"
      ></path>
    </svg>
  )
}

export function Pagination() {
  return (
    <div className="mt-6 flex items-center justify-between space-x-3 text-textColorBlack">
      <Link href="/" className="flex items-center">
        <Icon className="rotate-180" />
        Previous: Overview
      </Link>
      <Link href="/" className="flex items-center">
        Next: Overview
        <Icon />
      </Link>
    </div>
  )
}

export default Pagination
