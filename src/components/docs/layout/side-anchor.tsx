import { useEffect, useState } from 'react'
import clsx from 'clsx'
import { padTitleToId } from '@/utils'

export type ISideAnchorItem = { title: string }

export function DocsSideAnchor({ datas }: { datas: ISideAnchorItem[] }) {
  const [activeId, setActiveId] = useState('')

  const handleIntersect = (entries: IntersectionObserverEntry[]) => {
    const visibleEntries = entries
      .filter((entry) => entry.isIntersecting)
      .sort((a, b) => {
        const aRect = a.target.getBoundingClientRect()
        const bRect = b.target.getBoundingClientRect()
        return aRect.top - bRect.top
      })

    if (visibleEntries.length > 0) {
      setActiveId(visibleEntries[0].target.id)
    }
  }

  useEffect(() => {
    const observer = new IntersectionObserver(handleIntersect, {
      threshold: 0.7,
      rootMargin: '-50px 0px -50% 0px',
    })

    const sectionIds = datas.map((data) => padTitleToId(data.title))
    sectionIds.forEach((id) => {
      const $el = document.getElementById(id)
      if ($el) {
        observer.observe($el)
      }
    })
    return () => observer.disconnect()
  }, [datas])

  if (!Array.isArray(datas) || datas.length === 0) {
    return null
  }

  return (
    <div className="relative p-6">
      <h3 className="mb-4 text-base/6 font-semibold">On this page</h3>
      {Array.isArray(datas) && datas.length > 0 && (
        <ol className="list-disc ps-[16px]">
          {datas.map((data, index) => {
            return (
              <li
                className={clsx('mb-3 ps-0 text-sm/[22px]', {
                  'text-textColorLink': padTitleToId(data.title) === activeId,
                })}
                key={index}
              >
                <a href={`#${padTitleToId(data.title)}`}>{data.title}</a>
              </li>
            )
          })}
        </ol>
      )}
    </div>
  )
}

export default DocsSideAnchor
