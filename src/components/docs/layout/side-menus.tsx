import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import clsx from 'clsx'
// import { CaretRightOutlined } from '@ant-design/icons'
import { useMenu, IMenusItem } from '@/contexts'
import { CaretRightOutlined } from '../components'

export interface ISideMenuItemProps extends IMenusItem {
  isRoot?: boolean
  children?: ISideMenuItemProps[]
  onClick?: (v: IMenusItem) => void
}

export function SideMenuItem({
  title,
  url,
  children,
  isRoot,
  onClick,
}: ISideMenuItemProps) {
  const router = useRouter()
  const [isOpen, setIsOpen] = useState(true)
  const hasChildren = Array.isArray(children) && children.length > 0

  const checkAct = (url: string) => {
    return router.asPath === url
  }
  return (
    <div className="relative">
      <div className="relative flex items-center py-2 pl-1 pr-2">
        {hasChildren && (
          <CaretRightOutlined
            className={clsx('mr-2 cursor-pointer transition-all', {
              'rotate-90': isOpen,
            })}
            onClick={() => setIsOpen(!isOpen)}
          />
        )}
        <Link href={url || '#'}>
          <span
            className={clsx(
              'text-sm/[22px] transition-colors hover:text-textColorLink',
              {
                'font-semibold': isRoot,
                'text-textColorLink': url && checkAct(url),
              },
            )}
            onClick={() => {
              if (!isOpen) {
                setIsOpen(true)
              }
              onClick?.({ title, url })
            }}
          >
            {title}
          </span>
        </Link>
      </div>
      {hasChildren && isOpen && (
        <ol className="pl-5">
          {children.map((child, index) => {
            return <SideMenuItem key={index} {...child} onClick={onClick} />
          })}
        </ol>
      )}
    </div>
  )
}

export function SideMenus({ onClick }: { onClick?: (v: IMenusItem) => void }) {
  const { menus } = useMenu()

  return (
    <div className="max-h-[100vh] overflow-y-auto">
      {menus.map((data, index) => {
        return (
          <SideMenuItem
            key={`${data.title}-${index}`}
            isRoot
            {...data}
            onClick={onClick}
          />
        )
      })}
    </div>
  )
}

export default SideMenus
