import clsx from 'clsx'
import { DocsTableProps } from './table'
export const DocsReverseTable: React.FC<DocsTableProps> = (
  props: DocsTableProps,
) => {
  return (
    <div className="relative text-sm/[22px] text-textColorBlack">
      {(props?.dataSource || []).map((v, i) => {
        return (
          <div key={v.title}>
            <div className="flex">
              <div
                className={clsx(
                  'w-[260px] flex-shrink-0 border border-solid border-tableHeaderBorderPrimary bg-tableHeaderPrimary px-3 py-2.5 font-semibold max-md:w-[200px]',
                  {
                    'border-t-0': i > 0,
                    'rounded-tl-lg': i === 0,
                    'rounded-bl-lg': i === props?.dataSource?.length - 1,
                  },
                )}
              >
                {v.title}
              </div>
              <div
                className={clsx(
                  'flex-1 border border-l-0 border-solid border-[#DFDFDF] px-3 py-2.5',
                  {
                    'border-t-0': i > 0,
                    'rounded-tr-lg': i === 0,
                    'rounded-br-lg': i === props?.dataSource?.length - 1,
                  },
                )}
              >
                {v.value}
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}
export default DocsReverseTable
