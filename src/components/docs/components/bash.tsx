import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { base16AteliersulphurpoolLight as style } from 'react-syntax-highlighter/dist/cjs/styles/prism'
import { Code } from './code'

export const DocsBash = ({ codeString }: { codeString: string }) => {
  return (
    <Code codeString={codeString}>
      <SyntaxHighlighter
        language="bash"
        wrapLongLines
        style={style}
        customStyle={{ margin: 0 }}
      >
        {codeString}
      </SyntaxHighlighter>
    </Code>
  )
}
export default DocsBash
