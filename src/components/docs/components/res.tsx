import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { base16AteliersulphurpoolLight as style } from 'react-syntax-highlighter/dist/cjs/styles/prism'
import { Code } from './code'

export const DocsResJson = ({ data }: { data: Record<string, unknown> }) => {
  const codeString = JSON.stringify(data, null, 2)
  return (
    <Code codeString={codeString}>
      <SyntaxHighlighter
        language="json"
        showLineNumbers
        wrapLongLines
        style={style}
        lineProps={{
          className: 'line-code',
        }}
        customStyle={{ margin: 0 }}
      >
        {codeString}
      </SyntaxHighlighter>
    </Code>
  )
}
export default DocsResJson
