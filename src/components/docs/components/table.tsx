import React, { useState, Fragment } from 'react'
import clsx from 'clsx'
import { CaretRightOutlined } from './icon'
import styles from './table.module.css'

export interface DocsTableProps {
  columns: Array<{
    title: string
    dataIndex: string
    key?: string
    width?: string
    render?: (
      value: any,
      record: any,
      index: number,
      column: any,
    ) => React.ReactNode
  }>
  dataSource: Array<{
    [key: string]: any
    children?: Array<{ [key: string]: any }>
  }>
  rowKey?: string
  reverse?: boolean
  border?: boolean
}

export const DocsTable: React.FC<DocsTableProps> = (props: DocsTableProps) => {
  const { columns, dataSource, rowKey = '_id', reverse, border = false } = props
  const [expandedRows, setExpandedRows] = useState<{ [key: string]: boolean }>(
    {},
  )

  // 处理展开/折叠
  const toggleRow = (id: string) => {
    setExpandedRows((prev) => ({
      ...prev,
      [id]: !prev[id],
    }))
  }

  // 递归渲染行
  const renderRow = (row: any, index: number, level = 0) => {
    const isExpanded = expandedRows[row[rowKey]]
    const hasChildren =
      row.children && Array.isArray(row.children) && row.children.length > 0

    return (
      <Fragment key={`${row[rowKey]}-${index}`}>
        <tr
          className={clsx('break-all', {
            'cursor-pointer hover:bg-hoverBgRgba': hasChildren,
          })}
          onClick={() => {
            if (!hasChildren) return
            toggleRow(row[rowKey])
          }}
        >
          {columns.map((column, idx) => (
            <td
              key={`${row[rowKey]}-${column.key || column.dataIndex}`}
              className={clsx(
                'relative border border-[#DFDFDF] px-3 py-2.5 text-left text-sm/[22px]',
                {
                  // 'rounded-bl-lg': index === source.length - 1 && idx === 0,
                  // 'rounded-br-lg': index === source.length - 1 && idx === columns.length - 1,
                  'border-t-0': index >= 0,
                  'border-l-0': idx > 0,
                  'border-r-0': idx < columns.length - 1 && !border,
                },
              )}
              style={{
                paddingLeft: idx === 0 ? 12 + level * 16 + 'px' : undefined,
              }}
            >
              {idx === 0 && hasChildren && (
                <button className="mr-1 size-[12px] align-middle text-[8px]/[12px] text-[#CCCCCC] focus:outline-none">
                  <CaretRightOutlined
                    className={clsx('transition-all', {
                      'rotate-90': isExpanded,
                    })}
                  />
                </button>
              )}
              {column.render
                ? column.render(row[column.dataIndex], row, index, column)
                : row[column.dataIndex]}
            </td>
          ))}
        </tr>
        {isExpanded &&
          hasChildren &&
          row.children.map((child: any, idx: number) =>
            renderRow(child, idx, level + 1),
          )}
      </Fragment>
    )
  }

  return (
    <div className={clsx('overflow-x-auto rounded-lg', styles.table)}>
      <table className="min-w-full table-fixed border-separate border-spacing-0">
        {columns && columns.length > 0 && !reverse && (
          <thead>
            <tr>
              {columns.map((column, index) => (
                <th
                  key={`${column.key}-${index}`}
                  className={clsx(
                    'relative whitespace-nowrap border border-tableHeaderBorderPrimary bg-tableHeaderPrimary px-3 py-2.5 text-left text-sm/[22px] tracking-wider text-textColorBlack',
                    {
                      'rounded-tl-lg': index === 0,
                      'rounded-tr-lg': index === columns.length - 1,
                      'border-l-0': index > 0,
                    },
                    {
                      'border-r-0': index < columns.length - 1 && !border,
                    },
                  )}
                  style={{
                    width: column?.width,
                  }}
                >
                  {column.title}
                </th>
              ))}
            </tr>
          </thead>
        )}

        <tbody className="text-sm font-light text-textColorBlack">
          {dataSource.map((row, index) => renderRow(row, index))}
        </tbody>
      </table>
    </div>
  )
}

export default DocsTable
