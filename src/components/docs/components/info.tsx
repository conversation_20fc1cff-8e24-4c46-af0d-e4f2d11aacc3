const Icon = () => {
  return (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="15447"
      width="16"
      height="16"
    >
      <path
        d="M918.613333 672.981333Q949.333333 595.413333 949.333333 512t-30.72-160.981333q-33.322667-84.224-97.365333-148.266667-64.064-64.042667-148.266667-97.386667Q595.413333 74.666667 512 74.666667t-160.981333 30.72q-84.224 33.322667-148.266667 97.365333-64.042667 64.042667-97.386667 148.266667Q74.666667 428.586667 74.666667 512t30.72 160.981333q33.322667 84.202667 97.365333 148.266667 64.042667 64.042667 148.266667 97.386667Q428.586667 949.333333 512 949.333333t160.981333-30.72q84.202667-33.322667 148.266667-97.365333 64.042667-64.064 97.386667-148.266667z m-59.498666-298.410666Q885.333333 440.789333 885.333333 512t-26.218666 137.429333q-28.458667 71.893333-83.136 126.549334-54.656 54.677333-126.549334 83.136Q583.210667 885.333333 512 885.333333t-137.429333-26.218666q-71.893333-28.458667-126.549334-83.136-54.677333-54.656-83.136-126.549334Q138.666667 583.210667 138.666667 512t26.218666-137.429333q28.458667-71.893333 83.136-126.549334 54.656-54.677333 126.549334-83.136Q440.789333 138.666667 512 138.666667t137.429333 26.218666q71.893333 28.458667 126.549334 83.136 54.677333 54.656 83.136 126.549334z"
        p-id="15448"
        fill="#0060BF"
      ></path>
      <path
        d="M480 448v-32h64v362.666667h-64V448z"
        p-id="15449"
        fill="#0060BF"
      ></path>
      <path
        d="M469.333333 320a42.666667 42.666667 0 1 0 85.333334 0 42.666667 42.666667 0 1 0-85.333334 0Z"
        p-id="15450"
        fill="#0060BF"
      ></path>
    </svg>
  )
}

export const Info = ({
  important,
  children,
}: React.PropsWithChildren<{ important: boolean }>) => {
  return (
    <div
      className="flex rounded bg-white px-4 py-2 text-sm text-[#707070]"
      style={{ background: 'rgba(0, 96, 191, 0.04)' }}
    >
      <div className="mr-2 size-4 pt-[2px]">
        <Icon />
      </div>
      <div className="docs-info">
        {important && (
          <div className="mb-2 font-semibold text-[#707070]">Important!</div>
        )}
        {children}
      </div>
    </div>
  )
}
export default Info
