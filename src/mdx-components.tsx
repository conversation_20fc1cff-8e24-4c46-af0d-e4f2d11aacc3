import React, { ReactNode } from 'react'
import Link from 'next/link'
import type { MDXComponents } from 'mdx/types'

function getTextContent(node: React.ReactNode): string {
  if (typeof node === 'string' || typeof node === 'number') {
    return String(node)
  }

  if (React.isValidElement(node)) {
    if (node.type === 'small') {
      return ''
    }

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    return getTextContent(node.props.children)
  }

  if (Array.isArray(node)) {
    return node.map(getTextContent).join('')
  }

  return '' // If the node is neither text nor a React element
}

function slugify(str: React.ReactNode) {
  return getTextContent(str)
    .toLowerCase()
    .trim() // Remove whitespace from both ends of a string
    .replace(/\s+/g, '-') // Replace spaces with -
    .replace(/&/g, '-and-') // Replace & with 'and'
    .replace(/[^\w\-]+/g, '') // Remove all non-word characters except for -
    .replace(/\-\-+/g, '-') // Replace multiple - with single -
}

function createHeading(level: 1 | 2 | 3 | 4 | 5 | 6) {
  // eslint-disable-next-line react/display-name
  return ({ children }: React.PropsWithChildren) => {
    const slug = slugify(children)
    return React.createElement(`h${level}`, { id: slug }, [
      React.createElement(
        'a',
        {
          href: `#${slug}`,
          key: `link-${slug}`,
          className: 'anchor',
        },
        children,
      ),
    ])
  }
}

export function useMDXComponents(components: MDXComponents): MDXComponents {
  return {
    // Allows customizing built-in components, e.g. to add styling.
    // h1: ({ children }) => <h1 style={{ fontSize: "100px" }}>{children}</h1>,
    ...components,

    h2: createHeading(2),
    h3: createHeading(3),
    h4: createHeading(4),
    h5: createHeading(5),
    h6: createHeading(6),

    a(props: any) {
      return <Link {...props} />
    },

    code({ children }: { children: string | ReactNode }) {
      if (typeof children !== 'string') {
        return <code>{children}</code>
      }

      if (children.startsWith('<')) {
        return <code>{children}</code>
      }

      return (
        <code>
          {children
            .split(/(<[^>]+>)/g)
            .map((part, i) =>
              part.startsWith('<') && part.endsWith('>') ? (
                <var key={i}>{part}</var>
              ) : (
                part
              ),
            )}
        </code>
      )
    },
  }
}
