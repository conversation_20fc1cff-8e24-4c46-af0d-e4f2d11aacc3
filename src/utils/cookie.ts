import Cookies from 'js-cookie'

/**
 * 清除顶级域名的cookies
 * @param cookieNames 要清除的cookie名称数组
 */
export const clearTopLevelCookies = (cookieNames: string[]) => {
  // 获取当前域名
  const domain = window.location.hostname.split('.').slice(-2).join('.')
  console.log('domain', domain)
  cookieNames.forEach((name) => {
    // 清除根路径的cookie
    Cookies.remove(name, { path: '/' })
    // 清除顶级域名的cookie
    Cookies.remove(name, {
      path: '/',
      domain: `.${domain}`, // 添加点号以匹配子域名
    })
  })
}

/**
 * 清除所有cookies
 */
export const clearAllCookies = () => {
  const cookies = Cookies.get()
  const cookieNames = Object.keys(cookies)
  clearTopLevelCookies(cookieNames)
}
