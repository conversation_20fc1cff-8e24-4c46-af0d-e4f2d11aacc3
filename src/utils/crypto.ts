import CryptoJS from 'crypto-js'

interface CryptoUtils {
  encrypt: (str: string, key: string, iv: string) => string
  decrypt: (str: string, key: string, iv: string) => string
}

/**
 * AES 加密解密工具
 * 使用 CBC 模式和 Pkcs7 填充
 */
const cryptoUtils: CryptoUtils = {
  /**
   * AES 加密
   * @param str 需要加密的字符串
   * @param key 密钥
   * @param iv 初始向量
   * @returns 加密后的字符串
   */
  encrypt(str: string, key: string, iv: string): string {
    const keyBytes = CryptoJS.enc.Utf8.parse(key)
    const ivBytes = CryptoJS.enc.Utf8.parse(iv)
    const encrypted = CryptoJS.AES.encrypt(str, keyBytes, {
      iv: ivBytes,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    })
    return encrypted.toString()
  },

  /**
   * AES 解密
   * @param str 需要解密的字符串
   * @param key 密钥
   * @param iv 初始向量
   * @returns 解密后的字符串
   */
  decrypt(str: string, key: string, iv: string): string {
    const keyBytes = CryptoJS.enc.Utf8.parse(key)
    const ivBytes = CryptoJS.enc.Utf8.parse(iv)
    const decrypted = CryptoJS.AES.decrypt(str, keyBytes, {
      iv: ivBytes,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    })
    return decrypted.toString(CryptoJS.enc.Utf8)
  },
}

export default cryptoUtils
