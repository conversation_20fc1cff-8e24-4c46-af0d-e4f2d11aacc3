type RequestOptions = Omit<RequestInit, 'method'>

export async function request<T>(
  url: string,
  method: string,
  options?: RequestOptions,
): Promise<T> {
  const headers = {
    'Content-Type': 'application/json',
    ...options?.headers,
  }

  console.log(`[fetch] url=>${new Date().toLocaleString()}=>`, url)

  const response = await fetch(url, {
    method,
    headers,
    ...options,
  })

  console.log(
    `[fetch] response=>${new Date().toLocaleString()}=>`,
    response.status,
  )

  if (!response.ok) {
    throw new Error(`Request failed with status ${response.status}`)
  }

  return response.json() as T
}

/**
 * GET 请求
 * @param url
 * @param options
 * @returns
 */
export function get<T>(url: string, options?: RequestOptions): Promise<T> {
  return request<T>(url, 'GET', options)
}

/**
 * POST 请求
 * @param url
 * @param body
 * @param options
 * @returns
 */
export function post<T>(
  url: string,
  body: any,
  options?: RequestOptions,
): Promise<T> {
  return request<T>(url, 'POST', {
    ...options,
    body: JSON.stringify(body),
  })
}

/**
 * PUT 请求
 * @param url
 * @param body
 * @param options
 * @returns
 */
export function put<T>(
  url: string,
  body: any,
  options?: RequestOptions,
): Promise<T> {
  return request<T>(url, 'PUT', {
    ...options,
    body: JSON.stringify(body),
  })
}
