import { IResponse } from '@/api'

const MapType = {
  integer: 0,
  string: '',
  object: {},
  array: [],
}

/**
 * 将 responses 转换为 resData，规则为 MapType
 */
export function transformResponseToData(responses: IResponse[]) {
  const result: { [key: string]: any } = {}

  responses.forEach((item) => {
    const type = item.type as keyof typeof MapType
    const name = item.name as keyof typeof MapType
    if (item.children) {
      // 如果有子项，递归处理
      if (type === 'array') {
        result[name] = [] // 初始化为数组
        result[name].push(transformResponseToData(item.children)) // 添加一个空对象
      } else {
        result[name] = transformResponseToData(item.children)
      }
    } else {
      // 如果没有子项，直接根据 MapType 赋值
      result[name] = MapType[type]
    }
  })

  return result
}

/**
 * title 处理为 id
 * @param title
 * @returns
 */
export function padTitleToId(title: string) {
  return title
    .replace(/`([^`]+)`/g, '$1') // Remove inline code formatting
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .trim()
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .toLowerCase()
}

interface TreeData {
  [key: string]: any
}

type TreeCallback<T extends TreeData, R> = (
  data: T,
  index: number,
  array: T[],
  parent?: T,
) => R

export function treeEach<T extends TreeData>(
  treeData: T[],
  callback: TreeCallback<T, void>,
  parent?: T,
) {
  for (let i = 0; i < treeData.length; i++) {
    const data = treeData[i]
    callback(data, i, treeData, parent)

    const children = data?.children
    if (Array.isArray(children) && children.length) {
      treeEach(children, callback, data)
    }
  }
}

export function s4() {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
}

export function assignSourceId(datas: TreeData[]) {
  treeEach(datas, (v) => {
    Object.assign(v, { _id: s4() + s4() })
  })
  return datas as any[]
}
