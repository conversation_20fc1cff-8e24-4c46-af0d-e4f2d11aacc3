import Link from 'next/link'
export const meta = {
  slug: 'conditions',
  title: 'FS API Terms and Conditions',
  updatedAt: '2025/03/31',
  noWrap: true,
  anchors: [
    'Introduction',
    'Purpose',
    'Customer Responsibility',
    'Usage Restrictions',
    'Warranty Disclaimer',
    'Limitation of Liability',
    'Intellectual Property Rights and Ownership',
    'Data Collection and Use',
    'Confidential Information',
    'Termination and Survival',
    'Miscellaneous',
  ],
  breadcrumbs: [],
}

export const version = 'v20250522'

## FS API Terms and Conditions

### Introduction

When customer access FS Developer Portal, by checking "By checking this box, I acknowledge that I have read and accepthe FS API Terms and Conditions", it means that you and your company ("you","your"or Customer") and <Link href="https://fs.com" className='text-textColorLink'>FS.com</Link> ("FS” or “we") have reached an agreement and agree to accept all the provisions of this Agreement.

These Terms and Conditions govern your use of FS’s application programming interfaces (“API(s)”) and related services (collectively, the “API Services”) and constitute a binding agreement between Customer and FS. Hereinafter, these Terms and Conditions may be referred to as either "Terms and Conditions" or this "Agreement".

FS reserves the right to modify these Terms and Conditions at any time without prior notice. You agree to comply with the most up-to-date version of this Agreement as published on the FS website. Any changes to this Agreement will be posted on the platform, and your continued use of the platform and/or API Services after such modifications will constitute your acceptance of the revised terms.

<hr />

### Purpose

Customer shall use the API Services exclusively for transmitting data to and receiving data from FS. Throughout the term of this Agreement, Customer agrees to implement and maintain communication methods utilizing the APIs (the “Communication Methods”), with careful consideration given to authentication, security, confidentiality, and testing. Customer is responsible for reviewing mailboxes and other electronic data repositories, maintaining relevant logs and records, and issuing control confirmations and notifications as needed, at a frequency and under conditions determined during the development of the Communication Methods.

<hr />

### Customer Responsibility

#### Equipment, Software, and Third-Party Responsibility

Customer shall, at its own expense, provide and maintain all necessary equipment, software, services, and testing required to effectively and reliably meet the above requirements. If Customer engages a third party for any services, Customer remains fully responsible for any actions, failures, or omissions of that third party.

#### Data Backup and Protection

Customer is solely responsible for maintaining daily backups and ensuring the protection of its data and software against loss, damage, or corruption. Additionally, the customer bears full responsibility for restoring any lost, damaged, or corrupted data—including, but not limited to, data stored on disk files and memory—as, as well as any affected software during the performance of API Services.

#### Account Registration and Security

To access the API, customer may be required to register and set up an authorized account with login credentials. Customer must ensure that registration information remains accurate and up to date for as long as it uses the API. Customer shall not share its login credentials with any third party, nor shall it misrepresent or conceal its identity when using the API.

Customer is fully responsible for all activities conducted under its credentials, including API calls made using its issued credentials. This responsibility applies even if such usage does not comply with Customer’s internal procedures or results from its failure to protect the credentials. If Customer suspects that its credentials or account have been compromised—including unauthorized access, use, or disclosure of account information, passwords, or usernames—Customer must immediately notify its Sales Account Manager at FS.

Customer agree that any individual or entity authorized to use the API (“Users”) shall have the authority to act within the assigned roles and permissions when utilizing the credentials.

<hr />

### Usage Restrictions

#### Customer shall not do the following:

A. Use or access the API Materials to replicate, imitate, or compete with any FS offerings;
B. Alter, obscure, or remove any proprietary notices or markings contained in or associated with the API Materials;
C. Attempt to reverse engineer, decompile, disassemble, or otherwise derive the source code, trade secrets, or proprietary know-how of any FS offerings;
D. Sell, loan, rent, lease, sublicense, distribute, or otherwise transfer or encumber (e.g., through a lien or security interest) the API Materials.

<hr />

### Warranty Disclaimer

Customer assumes full responsibility for the use of API Materials. FS provides the API Materials on an “as is” and “as available” basis. To the maximum extent permitted by applicable law, FS makes no representations or warranties of any kind and expressly disclaims all warranties and conditions, including but not limited to warranties of merchantability, fitness for a particular purpose, accuracy, non-infringement, availability, and uninterrupted operation. FS does not guarantee that the API Materials or their use will be error-free or free from defects.

<hr />

### Limitation of Liability

Under no circumstances, and notwithstanding the failure of any remedy's essential purpose as set forth herein, shall FS or its affiliates be liable for: (a) any incidental, indirect, special, punitive, or consequential damages, including but not limited to, loss of profits, business, revenues, or savings, even if FS has been advised of the possibility of such damages or if such damages are otherwise foreseeable, regardless of whether the claim for such liability is based on breach of contract, warranty, negligence, strict liability, or any other theory of liability; (b) any claims, demands, or actions brought against the customer by any third party; (c) any loss or claim arising out of or in connection with the customer’s implementation of any conclusions or recommendations provided by FS or its affiliates, based on, resulting from, or related to the API services; or (d) any unavailability of the API services, or any lost, damaged, or corrupted data or software. In the event of any liability incurred by FS or any of its affiliates, the total liability of FS and its affiliates for damages, regardless of the cause, shall not exceed $500.

<hr />

### Intellectual Property Rights and Ownership

A. FS retains all rights, title, and interest in and to the FS APIs and FS Data. No rights, title, or interest are transferred to customer. FS reserves all intellectual property rights not expressly granted under these Terms.
B. Customer may not publicly display any marks or logos appearing in the FS Data or FS Documentation without the prior written consent of FS.
C. All inventions, product improvements, and modifications to any FS API or FS Data that are conceived or created by FS, whether in whole or in part, based on your feedback, suggestions, or recommended improvements, are the exclusive property of FS. All rights, title, and interest in such inventions, product improvements, or modifications to FS APIs or FS Data will vest solely in FS.

<hr />

### Data Collection and Use

FS may monitor and collect technical data and other information related to your use of FS APIs and associated activities. Customer agree to waive FS’s confidentiality obligations to the extent necessary for FS to freely use and share this information and data. This may include, but is not limited to, confirming compliance with these Terms, supporting FS APIs, improving or enhancing FS APIs, and developing, promoting, marketing, supporting, and enhancing FS products and offerings.

<hr />

### Confidential Information

Each of the parties may have access or be exposed to (through the API Materials or other means) materials, data, or information whether in written, oral, electronic, website-based, or other forms, that a reasonable person would know is not generally known to the public (collectively, “Confidential Information”). The recipient shall keep all confidential information strictly confidential until the confidential information becomes publicly known, using at least the same degree of care as it uses to protect its own confidential information, but no less than reasonable care. Notwithstanding any provisions to the contrary in these API Terms, your confidentiality obligations concerning technical information about current products and services, as well as all information about unreleased FS Offerings, shall never expire, subject to local law.

The recipient may share Confidential Information only with its employees who need to know such information in furtherance of the business relationship between customer and FS, provided that these employees are bound by legally enforceable confidentiality obligations that are at least as restrictive as those outlined in these API Terms. The recipient is fully liable for any breach of this section by its personnel, including contractors.

These confidentiality obligations do not apply to any Confidential Information that: (a) the recipient can demonstrate was already in its possession before its disclosure hereunder; (b) is or becomes publicly available through no fault of the recipient or its personnel; or (c) is rightfully received from a third party who has no duty of confidentiality. If the recipient is required by a government body or court of law to disclose any Confidential Information, to the extent permitted by law, the recipient agrees to give the discloser reasonable advance notice so that the discloser may contest the disclosure or seek a protective order. The recipient acknowledges that damages for improper disclosure of Confidential Information may be irreparable and that the discloser shall be entitled to seek equitable relief, including injunction and preliminary injunction, in addition to all other remedies available at law or in equity (if any applicable judicial jurisdiction exists）. Notwithstanding any separate confidentiality agreement between FS and you, you authorize and agree that the information regarding your business with FS and information you provide to FS in connection with your use of the API Materials may be accessed and used by FS and their employees for sales and marketing purposes and for any purpose related to the API Materials or the relationship between you and FS.

<hr />

### Termination and Survival

This Agreement shall remain in effect until terminated by either party. Either party may terminate this Agreement at any time upon written notice, which notice shall specify the effective date of termination. All notices shall be delivered in writing and either sent electronically or mailed to the other party. The termination of this Agreement shall not affect the enforceability of, or the respective obligations or rights of the parties arising in connection with, any communication under this Agreement prior to the effective date of termination.

Upon termination, all rights and obligations of the parties under this Agreement will automatically terminate except for any right of action occurring prior to termination, and obligations that expressly or by implication are intended to survive termination (including, but not limited to, limitation of liability, indemnity, confidentiality, and this survival provision).

<hr />

### Miscellaneous

As this agreement involves API management, the FS Terms and Conditions and FS Privacy and Cookies, and other policy pages are considered as higher-level agreements. Any matters not covered in this agreement may refer to the relevant provisions of the higher-level agreements.

FS may assign all or any portion of its rights or obligations with respect to the performance of the API Services without Customer's consent. Customer may not assign these Terms and Conditions, or any of its rights or obligations herein without the prior written consent of FS. Subject to the restrictions in assignment contained herein, these Terms and Conditions will be binding on and inure to the benefit of the parties hereto and their successors and assigns.

This Agreement constitutes the complete agreement of the parties with respect to the subject matter hereof and supersedes all prior representations or agreements, whether oral or written.

The relationship between FS and Customer is that of independent contractors and not that of employer/employee, partnership or joint venture.

If any term or condition of this Agreement is found by an applicable law to be invalid, illegal or otherwise unenforceable, the same shall not affect the other terms or conditions hereof or thereof or the whole of this Agreement.

Customer consent to receive communications from FS electronically, including through e-mails, in-app push notices, notices, and messages posted on FS API or in your Message Center and other communications made available to you on a desktop or mobile device. Customer agree that all agreements, notices, disclosures, and other communications that we provide to you electronically satisfy any legal requirement that such communications be in writing.

Any delay or failure by either party to exercise any right or remedy will not constitute a waiver of that party to thereafter enforce such rights.
