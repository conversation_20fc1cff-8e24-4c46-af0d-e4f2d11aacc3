import Link from 'next/link'
import { Info, Login } from '@/components'

export const meta = {
  slug: 'startedGuide',
  title: 'Getting Started Guide',
  updatedAt: '2025/03/31',
  anchors: [
    'Prerequisites for Using FS Developer Portal APIs',
    'Creating FS Business Account',
    'Don’t have an FS account?',
    'Already have an FS account?',
    'Requesting FS APIs Credentials',
  ],
  breadcrumbs: [
    {
      title: 'Guides',
    },
  ],
}

## Getting Started Guide

FS Developer Portal is an open platform for developers and partners to seamlessly integrate our services into their applications for real-time business data exchanges.

FS APIs enable you to manage order and access detailed product information such as inventory, and specifications, all built on modern RESTful principles and secured by industry-standard authentication.

### Prerequisites for Using FS Developer Portal APIs

To use FS Developer Portal APIs, you need:

1. An active **FS Business Account**. See <Link href="#creating-fs-business-account" className='text-textColorLink'>Creating FS Business Account</Link>.
2. Become an administrator of the **FS Business Account**.
3. Access to the **FS Developer Portal Workbench**, apply for credentials, including a client ID and secret. See <Link href="#requesting-fs-apis-credentials" className='text-textColorLink'>Requesting FS APIs Credentials on this process</Link>.
4. Knowledge of RESTful API principles.
5. A development environment with tools to make HTTP requests, such as cURL, Postman, or a programming language like Python.

### Creating FS Business Account

#### Don’t have an FS account?

1. To create an FS Business account, click <Login/>.
2. Click **Create an Account** at the bottom of the page.
3. Choose **Business Account**, and click **Continue**.
4. Provide the following account information:

   - First Name/Last Name: Your first and last name.
   - Email: The email address for your account.
   - Password —A password for accessing your account.
   - Organization Information: The name and address details of your business.

5. Acknowledge the **FS Terms of Use** by selecting the check box.
6. Click **Create an Account**. A verification email sent message appears.
7. Verify your email address and activate your new FS account using the link provided in the email.
8. Once your account is successfully activated, this account can be used to log in to both the <Link href="https://fs.com" className='text-textColorLink'>FS.com</Link> and <Link href="/" className='text-textColorLink'>FS Developer Portal</Link>.
9. Meanwhile, the system will review your business account application within 1-2 business days. You can check the progress in "**Case**" in FS Account Center.

#### Already have an FS account?

1. There are three ways to apply for FS Business Account application:

   - Access <Link href="https://www.fs.com/business_account.html" className='text-textColorLink'>FS Business Account Application</Link> in <Link href="https://fs.com" className='text-textColorLink'>FS.com</Link>. Please note that you need to log in first to access this page.
   - Access <Link href="/" className='text-textColorLink'>FS Developer Portal</Link> and <Login />, click Workbench.
   - Contact your account manager to apply.

2. Provide the Organization information.
3. Acknowledge the **FS Business Account Terms of Use** by selecting the check box.
4. Click **Submit**, the system will review your business account application within 1-2 business days. You can check the progress in "**Case**" in FS Account Center.

### Requesting FS APIs Credentials

Once you become an administrator of the FS Business Account:

1. Go to **Workbench**.
2. Click **Apply for Credentials**.
3. Provide the following account information:

   - Organization name.
   - Description of how you will use the API(s) (optional).
   - The API(s) you want to subscribe to.

4. Acknowledge the **FS API Terms and Conditions** by selecting the check box.
5. Click **Submit**, FS developer team will review and configure your request. This process typically takes 1-2 business days. If you have any questions, please contact your account manager.

<Info>
  Obtain your API client ID & client secret: once your API authorization request
  has been approved and created, FS technical team will assign and send a pair
  of client ID & client secret to you.
</Info>
