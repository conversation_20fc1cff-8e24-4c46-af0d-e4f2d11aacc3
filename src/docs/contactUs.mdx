export const meta = {
  slug: 'contactUs',
  title: 'Contact Us',
  updatedAt: '2025/03/31',
  anchors: [
    'Steps to Get Support',
    'Email Your Account Manager',
    'Account Manager Escalates the Issue',
    'Technical Team Provides Support',
  ],
  breadcrumbs: [],
}

## Contact Us

FS developer team can answer questions and resolve issues with FS Developer Portal. If you encounter any issues or need technical support while using our API platform, we are here to help.

### Steps to Get Support

#### 1. Email Your Account Manager

- Please contact your account manager via email to communicate your request or issue.
- Clearly outline your issue or request in the email, including:
  - Your App ID, organization name and contact information;
  - A detailed description of the issue (e.g., API call failures, documentation questions, etc.);
  - Screenshots or log files related to the issue (if applicable).
- Your account manager will respond to your email within 1-2 business days.

#### 2. Account Manager Escalates the Issue

- Based on the nature of your issue, the account manager will escalate it to the relevant technical team.
- If needed, the account manager may reach out to you for additional details to ensure the technical team can promptly address the problem.

#### 3. Technical Team Provides Support

- The technical team will contact you after receiving the issue to confirm the details.
- Depending on the complexity of the issue, the team may offer support through:
  - Remote meetings or phone calls to gain deeper insights;
  - Providing detailed technical solutions or guidance;
  - Assisting with troubleshooting and resolving the problem to ensure functionality is restored.
- For more complex issues, the technical team will coordinate with you to provide extended support if necessary.

We are committed to delivering efficient and professional support services. Please do not hesitate to contact us if you need further assistance!
