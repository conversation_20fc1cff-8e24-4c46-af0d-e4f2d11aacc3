import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { base16AteliersulphurpoolLight as style } from 'react-syntax-highlighter/dist/cjs/styles/prism'

export const meta = {
  slug: 'demo',
  title: 'Demo',
  updatedAt: '2025/03/31',
  anchors: ['This is a heading', 'Demo'],
  breadcrumbs: [],
}

## Demo

### This is a heading

This is a list in markdown:

- One
- Two
- Three

### Code

```bash
pnpm i dayjs
```

<SyntaxHighlighter
  language="bash"
  wrapLongLines
  style={style}
  customStyle={{ margin: 0 }}
>
  {'pnpm i dayjs'}
</SyntaxHighlighter>

```jsx
function MyCombobox({ items }) {
  // [!code highlight:2]
  const [selectedItems, setSelectedItems] = useState([])

  return (
    // [!code highlight:9]
    <Combobox value={selectedItems} onChange={setSelectedItems} multiple>
      {selectedItems.length > 0 && (
        <ul>
          {selectedItems.map((item) => (
            <li key={item}>{item}</li>
          ))}
        </ul>
      )}
      <Combobox.Input />
      <Combobox.Options>
        {items.map((item) => (
          <Combobox.Option key={item} value={item}>
            {item}
          </Combobox.Option>
        ))}
      </Combobox.Options>
    </Combobox>
  )
}
```

<SyntaxHighlighter
  language="tsx"
  wrapLongLines
  customStyle={{ margin: 0 }}
>
  {`
  function MyCombobox({ items }) {
    // [!code highlight:2]
    const [selectedItems, setSelectedItems] = useState([])

    return (
      // [!code highlight:9]
      <Combobox value={selectedItems} onChange={setSelectedItems} multiple>
        {selectedItems.length > 0 && (
          <ul>
            {selectedItems.map((item) => (
              <li key={item}>{item}</li>
            ))}
          </ul>
        )}
        <Combobox.Input />
        <Combobox.Options>
          {items.map((item) => (
            <Combobox.Option key={item} value={item}>
              {item}
            </Combobox.Option>
          ))}
        </Combobox.Options>
      </Combobox>
    )

}
`}

</SyntaxHighlighter>
