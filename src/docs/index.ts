import Demo, { meta as DemoMeta } from './demo.mdx'
import Conditions, { meta as ConditionsMeta } from './conditions.mdx'
import ContactUs, { meta as ContactUsMeta } from './contactUs.mdx'
import FsAuthentication, {
  meta as FsAuthenticationMeta,
} from './fsAuthentication.mdx'
import StartedGuide, { meta as StartedGuideMeta } from './startedGuide.mdx'

export interface IPageMeta {
  title: string
  slug: string
  updatedAt: string
  anchors: string[]
  breadcrumbs?: { title: string }[]
  noWrap?: boolean
}

export const MapMdx = {
  [DemoMeta.slug]: {
    meta: DemoMeta,
    Component: Demo,
  },
  [ConditionsMeta.slug]: {
    meta: ConditionsMeta,
    Component: Conditions,
  },
  [ContactUsMeta.slug]: {
    meta: ContactUsMeta,
    Component: ContactUs,
  },
  [FsAuthenticationMeta.slug]: {
    meta: FsAuthenticationMeta,
    Component: FsAuthentication,
  },
  [StartedGuideMeta.slug]: {
    meta: StartedGuideMeta,
    Component: StartedGuide,
  },
}

export function getDocPageSlugs() {
  return Promise.resolve(Object.keys(MapMdx))
}

export function getDocPageBySlug(slug: string) {
  const { meta } = MapMdx[slug] as { meta: IPageMeta }
  return Promise.resolve({ ...meta })
}
