import Link from 'next/link'
import {
  DocsTable,
  DocsReverseTable,
  DocsRes<PERSON>son,
  DocsBash,
  Info,
} from '@/components'

export const meta = {
  slug: 'fsAuthentication',
  title: 'FS API Authentication and Authorization',
  updatedAt: '2025/03/31',
  anchors: ['EndPoint', 'Rate Limit', 'Obtain authorization code'],
  breadcrumbs: [
    {
      title: 'Guides',
    },
  ],
}

## FS API Authentication and Authorization

To use FS API services, please reference the <Link href="/guides/startedGuide" className='text-textColorLink !underline !underline-offset-4'>Getting Started Guide</Link> to obtain FS API credentials.

**Obtain your API account client ID & client secret**: once your API account request has been approved and created, FS team will assign and send a pair of client ID and client secret to you. You must provide the credentials per function as authentication in order to access FS API services.

<Info important>
  We highly recommend that you do not let any unauthorized personnel or 3rd
  party developers have access to your original pair of API client ID and client
  secret. Anyone with this information can access our FS API services as you.
  The system will automatically deactivate API credentials if there are no calls
  made with them within 90 days.
</Info>

### EndPoint

<DocsTable
  border
  columns={[
    {
      title: 'Enviroment',
      dataIndex: 'enviroment',
      key: 'enviroment',
    },
    {
      title: 'EndPoint',
      dataIndex: 'endPoint',
      key: 'endPoint',
    },
  ]}
  dataSource={[
    {
      enviroment: 'PROD',
      endPoint: 'https://open.fs.com',
    },
  ]}
/>

### Rate Limit

Rate limits are divided into minute intervals. This means you have a maximum number of requests you can submit to a specific function in a one-minute period.

<DocsReverseTable
  columns={[
    { title: 'title', dataIndex: 'title', key: 'title' },
    { title: 'value', dataIndex: 'value', key: 'value' },
  ]}
  dataSource={[
    {
      title: 'Per-client quota limit per second',
      value: '10 (May not be reached)',
    },
    {
      title: 'Interface frequency limit',
      value: '60 times/minute',
    },
  ]}
/>

### Obtain authorization code

#### Basic

<DocsReverseTable
  columns={[
    { title: 'title', dataIndex: 'title', key: 'title' },
    { title: 'value', dataIndex: 'value', key: 'value' },
  ]}
  dataSource={[
    {
      title: 'HTTP URL',
      value: '/oauth/token',
    },
    {
      title: 'HTTP METHOD',
      value: 'POST',
    },
    {
      title: 'Interface frequency limit',
      value: '10 times/minute',
    },
    {
      title: 'Permission requirements',
      value: 'No',
    },
  ]}
/>

#### Request header

<DocsTable
  border
  columns={[
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: 'Parameter',
      dataIndex: 'parameter',
      key: 'parameter',
    },
  ]}
  dataSource={[
    {
      name: 'Content-Type',
      type: 'String',
      parameter: 'application/x-www-form-urlencoded',
    },
  ]}
/>

#### Request parameters

To access FS API services, you must provide authentication information listed below:

<DocsTable
  border
  columns={[
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Required?',
      dataIndex: 'required',
      key: 'required',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
    },
  ]}
  dataSource={[
    {
      name: 'client_id',
      required: 'Yes',
      description: 'the unique client ID which FS team assigned to you',
      type: 'string',
    },
    {
      name: 'client_secret',
      required: 'Yes',
      description: 'the unique Secret Key which FS team assigned to you',
      type: 'string',
    },
    {
      name: 'grant_type',
      required: 'Yes',
      description: 'License Type: client_credentials',
      type: 'string',
    },
  ]}
/>

Example: HTTP, Request

<DocsBash
  codeString={`
  curl --location --request POST 'https://open.fs.com/oauth/token'
       --header 'Content-Type: application/x-www-form-urlencoded'
       --data-urlencode 'grant_type=client_credentials'
       --data-urlencode 'client_secret=your_client_secret'
       --data-urlencode 'client_id=your_client_id'
`}
/>

#### Response

<DocsTable
  border
  columns={[
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
  ]}
  dataSource={[
    {
      name: 'code',
      description:
        'Status code: 200 indicates successful response; 401 indicates authentication failure',
    },
    {
      name: 'data.access_token',
      description:
        'Authorization code: used for resource service data requests, the access_token into the subsequent interface header request header.',
    },
    {
      name: 'data.token_type',
      description: 'Authorization code type, default is Bearer Token',
    },
    {
      name: 'data.expires_in',
      description: 'Authorization code expiration time',
    },
  ]}
/>

Example: JSON, Response

<DocsResJson
  data={{
    code: 200,
    message: 'Success',
    traceId: 'jGSY624889657237573',
    extend: null,
    data: {
      access_token: 'your_access_token',
      token_type: 'Bearer',
      expires_in: 1799,
    },
  }}
/>
